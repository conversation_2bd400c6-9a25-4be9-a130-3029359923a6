use std::net::IpAddr;
use netstat2::{get_sockets_info, AddressFamilyFlags, ProtocolFlags};

use sysinfo::{System, SystemExt, ProcessExt, Pid};
use tokio::net::{TcpListener, TcpStream};
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use log::{info, error, warn};
use totp_rs::{Algorithm, TOTP};
use data_encoding::BASE32;
use serde::{Deserialize, Serialize};
use std::collections::HashSet;
use std::time::Duration;
use teralib::config::get_config_value;
use once_cell::sync::Lazy;
use tokio::sync::Mutex;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WhitelistConfig {
    pub allowed_endpoints: Vec<String>,
}

// 全局缓存，程序生命周期内持久存在
static WHITELIST_CACHE: Lazy<Mutex<Option<HashSet<String>>>> = Lazy::new(|| {
    Mutex::new(None)
});

/// 检查指定路径的 TERA.exe 是否正在运行（更精准）
pub fn is_game_running_precise(game_path: &str) -> bool {
    let mut system = System::new_all();
    system.refresh_all();
    for process in system.processes_by_name("TERA.exe") {
        if let Some(proc_path) = process.exe().to_str() {
            if proc_path.eq_ignore_ascii_case(game_path) {
                return true;
            }
        }
    }
    false
}

// 从远程获取白名单配置
async fn fetch_whitelist_config() -> Result<WhitelistConfig, String> {
    let whitelist_url = get_config_value("WHITELIST_URL");
    
    let client = reqwest::Client::builder()
        .timeout(Duration::from_secs(10))
        .build()
        .map_err(|e| format!("创建HTTP客户端失败: {}", e))?;

    let response = client.get(&whitelist_url)
        .send()
        .await
        .map_err(|e| format!("请求白名单配置失败: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("HTTP请求失败，状态码: {}", response.status()));
    }

    let config: WhitelistConfig = response.json()
        .await
        .map_err(|e| format!("解析白名单配置失败: {}", e))?;
    
    info!("白名单配置已获取，共 {} 个端点", config.allowed_endpoints.len());
    Ok(config)
}

// 获取缓存的白名单端点，如果缓存为空则从远程获取并缓存
async fn get_cached_whitelist_endpoints() -> Result<HashSet<String>, String> {
    let mut cache = WHITELIST_CACHE.lock().await;
    
    if let Some(ref cached_endpoints) = *cache {
        return Ok(cached_endpoints.clone());
    }
    
    info!("获取白名单配置...");
    
    // 获取白名单配置
    let whitelist_config = fetch_whitelist_config().await?;
    
    // 解析所有端点（包括域名解析）
    let resolved_endpoints = resolve_endpoints_to_ips(&whitelist_config.allowed_endpoints).await;
    
    // 缓存解析后的端点
    *cache = Some(resolved_endpoints.clone());
    
    info!("白名单配置已加载，共 {} 个端点", resolved_endpoints.len());
    Ok(resolved_endpoints)
}

// 解析域名为IP地址并生成完整的端点列表
async fn resolve_endpoints_to_ips(endpoints: &[String]) -> HashSet<String> {
    let mut resolved_endpoints = HashSet::new();
    
    for endpoint in endpoints {
        // 直接添加原始端点（可能是IP:端口）
        resolved_endpoints.insert(endpoint.clone());
        
        // 如果是域名:端口格式，尝试解析域名
        if let Some((domain, port)) = endpoint.split_once(':') {
            if !domain.chars().all(|c| c.is_ascii_digit() || c == '.') {
                // 这是域名，需要解析
                match tokio::net::lookup_host(endpoint).await {
                    Ok(addrs) => {
                        for addr in addrs {
                            let ip_endpoint = format!("{}:{}", addr.ip(), port);
                            resolved_endpoints.insert(ip_endpoint);
                        }
                    }
                    Err(e) => {
                        warn!("域名端点 {} 解析失败: {}", endpoint, e);
                    }
                }
            }
        }
    }
    
    resolved_endpoints
}



// 检查连接是否在白名单中（完全匹配IP:端口）
fn is_connection_whitelisted(allowed_endpoints: &HashSet<String>, remote_ip: IpAddr, remote_port: u16) -> bool {
    let endpoint = format!("{}:{}", remote_ip, remote_port);
    allowed_endpoints.contains(&endpoint)
}

// 外挂检测函数（使用缓存版本）
pub async fn detect_game_proxy_hijack_by_pid_with_whitelist(pid: u32) -> Result<bool, String> {
    // 获取缓存的白名单端点
    let allowed_endpoints = get_cached_whitelist_endpoints().await?;
    
    let af_flags = AddressFamilyFlags::all();
    let proto_flags = ProtocolFlags::TCP;
    let sockets_info = get_sockets_info(af_flags, proto_flags)
        .map_err(|e| format!("获取网络连接信息失败: {}", e))?;

    let mut found_connections = 0;

    for si in sockets_info {
        if si.associated_pids.contains(&pid) {
            found_connections += 1;
            if let netstat2::ProtocolSocketInfo::Tcp(tcp_si) = si.protocol_socket_info {
                let remote_ip = tcp_si.remote_addr;
                let remote_port = tcp_si.remote_port;

                // 检查是否在白名单中（完全匹配IP:端口）
                if !is_connection_whitelisted(&allowed_endpoints, remote_ip, remote_port) {
                    warn!("检测到可疑连接: {}:{} (不在白名单中)", remote_ip, remote_port);
                    
                    // 如果发现任何不在白名单中的连接，立即判定为外挂
                    return Ok(true);
                }
            }
        }
    }
    
    if found_connections > 0 {
        info!("外挂检测完成: 共检查 {} 个连接，未发现异常", found_connections);
    }
    Ok(false)
}



/// 检查指定PID的进程是否正在运行
pub fn is_process_running(pid: u32) -> bool {
    let mut system = System::new_all();
    system.refresh_all();
    system.process(Pid::from(pid as usize)).is_some()
}

/// 端口转发服务
pub async fn start_port_forward(
    listen_addr: &str,
    target_host: &str,
    target_port: u16,
    shutdown_signal: Arc<AtomicBool>
) -> Result<(), String> {
    let listener = TcpListener::bind(listen_addr).await
        .map_err(|e| format!("绑定监听地址 {} 失败: {}", listen_addr, e))?;
    
    info!("端口转发服务已启动，监听 {} -> {}:{}", listen_addr, target_host, target_port);
    
    loop {
        // 检查是否需要停止服务
        if shutdown_signal.load(Ordering::Relaxed) {
            info!("收到停止信号，端口转发服务正在关闭");
            break;
        }
        
        // 设置超时以便定期检查停止信号
        let accept_result = tokio::time::timeout(
            std::time::Duration::from_secs(1),
            listener.accept()
        ).await;
        
        match accept_result {
            Ok(Ok((client_stream, client_addr))) => {
                info!("接受来自 {} 的连接", client_addr);
                
                let target_host = target_host.to_string();
                let shutdown_signal_clone = Arc::clone(&shutdown_signal);
                
                // 为每个连接创建一个任务
                tokio::spawn(async move {
                    if let Err(e) = handle_connection(client_stream, &target_host, target_port, shutdown_signal_clone).await {
                        error!("处理连接失败: {}", e);
                    }
                });
            },
            Ok(Err(e)) => {
                error!("接受连接失败: {}", e);
            },
            Err(_) => {
                // 超时，继续循环检查停止信号
                continue;
            }
        }
    }
    
    info!("端口转发服务已停止");
    Ok(())
}

/// 处理单个连接的转发
async fn handle_connection(
    mut client_stream: TcpStream,
    target_host: &str,
    target_port: u16,
    shutdown_signal: Arc<AtomicBool>
) -> Result<(), String> {
    // 连接到目标服务器
    let target_addr = format!("{}:{}", target_host, target_port);
    let mut target_stream = TcpStream::connect(&target_addr).await
        .map_err(|e| format!("连接到目标服务器 {} 失败: {}", target_addr, e))?;
    
    info!("已建立到目标服务器 {} 的连接", target_addr);
    
    // 分割流以便双向转发
    let (mut client_read, mut client_write) = client_stream.split();
    let (mut target_read, mut target_write) = target_stream.split();
    
    // 客户端到服务器的转发
    let client_to_server = async {
        let mut buffer = [0u8; 8192];
        loop {
            if shutdown_signal.load(Ordering::Relaxed) {
                break;
            }
            
            match client_read.read(&mut buffer).await {
                Ok(0) => {
                    info!("客户端连接已关闭");
                    break;
                },
                Ok(n) => {
                    if let Err(e) = target_write.write_all(&buffer[..n]).await {
                        warn!("向目标服务器写入数据失败: {}", e);
                        break;
                    }
                },
                Err(e) => {
                    warn!("从客户端读取数据失败: {}", e);
                    break;
                }
            }
        }
    };
    
    // 服务器到客户端的转发
    let server_to_client = async {
        let mut buffer = [0u8; 8192];
        loop {
            if shutdown_signal.load(Ordering::Relaxed) {
                break;
            }
            
            match target_read.read(&mut buffer).await {
                Ok(0) => {
                    info!("目标服务器连接已关闭");
                    break;
                },
                Ok(n) => {
                    if let Err(e) = client_write.write_all(&buffer[..n]).await {
                        warn!("向客户端写入数据失败: {}", e);
                        break;
                    }
                },
                Err(e) => {
                    warn!("从目标服务器读取数据失败: {}", e);
                    break;
                }
            }
        }
    };
    
    // 同时运行双向转发
    tokio::select! {
        _ = client_to_server => {},
        _ = server_to_client => {},
    }
    
    info!("连接转发已结束");
    Ok(())
}

/// 生成TOTP令牌
fn generate_totp_token() -> Result<String, String> {
    let raw_secret = b"pR7xY2JqNbUvA3Lc";
    let secret_base32 = BASE32.encode(raw_secret);

    let decoded_secret = BASE32.decode(secret_base32.as_bytes())
        .map_err(|e| format!("解码密钥失败: {}", e))?;

    let totp = TOTP::new(Algorithm::SHA1, 6, 1, 30, decoded_secret)
        .map_err(|e| format!("创建TOTP失败: {}", e))?;

    let code = totp.generate_current()
        .map_err(|e| format!("生成令牌失败: {}", e))?;
    Ok(code)
}

/// 以管理员权限启动程序
fn try_launch_as_admin(path: &str, token: &str) -> Result<(), String> {
    use windows::{
        core::PCWSTR,
        Win32::{
            Foundation::HWND,
            UI::{
                Shell::ShellExecuteW,
                WindowsAndMessaging::{SHOW_WINDOW_CMD, SW_SHOWNORMAL},
            },
        },
    };
    use widestring::U16CString;

    let operation = U16CString::from_str("runas").map_err(|e| format!("转换操作字符串失败: {}", e))?;
    let file = U16CString::from_str(path).map_err(|e| format!("转换文件路径失败: {}", e))?;
    let params = U16CString::from_str(&format!("--token={}", token)).map_err(|e| format!("转换参数失败: {}", e))?;

    unsafe {
        let result = ShellExecuteW(
            HWND(0),
            PCWSTR::from_raw(operation.as_ptr()),
            PCWSTR::from_raw(file.as_ptr()),
            PCWSTR::from_raw(params.as_ptr()),
            PCWSTR::null(),
            SHOW_WINDOW_CMD(SW_SHOWNORMAL.0),
        );
        if result.0 <= 32 {
            Err(format!("ShellExecuteW 启动失败，错误码: {}", result.0))
        } else {
            Ok(())
        }
    }
}

/// 启动DPS插件
pub fn launch_dps_plugin() -> Result<String, String> {
    let current_dir = std::env::current_dir().map_err(|e| e.to_string())?;
    let shinra_meter_path = current_dir.join("ShinraMeter").join("ShinraMeter.exe");

    if shinra_meter_path.exists() {
        // 生成将军令
        let token = match generate_totp_token() {
            Ok(tok) => tok,
            Err(e) => {
                warn!("生成将军令失败: {}", e);
                String::new()
            }
        };

        match try_launch_as_admin(shinra_meter_path.to_str().unwrap(), &token) {
            Ok(_) => {
                info!("DPS插件以管理员权限启动成功");
                Ok("DPS插件启动成功".to_string())
            },
            Err(e) => {
                warn!("DPS插件以管理员权限启动失败: {}", e);
                Err(format!("DPS插件启动失败: {}", e))
            }
        }
    } else {
        let error_msg = format!("DPS插件不存在: {:?}", shinra_meter_path);
        warn!("{}", error_msg);
        Err(error_msg)
    }
}