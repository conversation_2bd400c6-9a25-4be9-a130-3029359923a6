use log::{error, info};
use serde_json::json;
use tauri::Window;
use tempfile::TempDir;
use std::path::Path;
use std::time::Duration;
use tokio::io::AsyncWriteExt;
use futures_util::StreamExt;
use teralib::config::get_config_value;

use std::env;

#[cfg(windows)]
use windows::core::PCWSTR;
#[cfg(windows)]
use windows::Win32::UI::Shell::ShellExecuteW;
#[cfg(windows)]
use windows::Win32::UI::WindowsAndMessaging::SW_SHOWNORMAL;
#[cfg(windows)]
use widestring::U16CString;

// 当前版本
const CURRENT_VERSION: &str = env!("CARGO_PKG_VERSION");

// ============================================================================
// 启动器自更新结构体
// ============================================================================

pub struct LauncherSelfUpdate;

impl LauncherSelfUpdate {
    /// 检查并执行自更新
    pub async fn check_and_update(window: &Window) -> Result<bool, String> {
        info!("开始检查启动器更新...");
        
        // 发送检查更新事件
        let _ = window.emit("launcher_update_status", json!({
            "status": "checking",
            "message": "正在检查启动器更新..."
        }));

        // 获取最新版本信息
        let latest_release = Self::get_latest_release().await?;
        let raw_tag = latest_release["tag_name"]
            .as_str()
            .ok_or("无法获取版本号")?;
        
        // 处理版本号：去掉 v 或 V 前缀，并去掉首尾空格
        let latest_version = raw_tag
            .trim()
            .trim_start_matches('v')
            .trim_start_matches('V')
            .trim();

        info!("版本比较详情: 原始标签='{}', 处理后最新版本='{}', 当前版本='{}'", 
              raw_tag, latest_version, CURRENT_VERSION);

        // 检查是否需要更新
        if latest_version == CURRENT_VERSION {
            let _ = window.emit("launcher_update_status", json!({
                "status": "latest",
                "message": "启动器已是最新版本",
                "version": CURRENT_VERSION
            }));
            info!("版本检查结果: 无需更新");
            return Ok(false);
        }

        // 获取下载链接
        let download_url = latest_release["assets"]
            .as_array()
            .ok_or("无法获取下载资源")?
            .iter()
            .find(|asset| asset["name"].as_str().unwrap_or("").ends_with(".exe"))
            .ok_or("找不到可执行文件")?["browser_download_url"]
            .as_str()
            .ok_or("无法获取下载链接")?;

        info!("版本检查结果: 需要从 {} 更新到 {}", CURRENT_VERSION, latest_version);

        info!("开始下载更新文件: {}", download_url);
        
        // 发送下载开始事件
        let _ = window.emit("launcher_update_status", json!({
            "status": "downloading",
            "message": "正在下载更新文件..."
        }));

        // 创建临时目录
        let temp_dir = TempDir::new().map_err(|e| format!("创建临时目录失败: {}", e))?;
        let temp_file = temp_dir.path().join("teralaunch_update.exe");

        // 使用更健壮的下载逻辑
        info!("开始下载到临时文件: {}", temp_file.display());
        
        match Self::download_file_with_timeout(download_url, &temp_file, &window).await {
            Ok(_) => {
                info!("下载完成，文件大小: {} bytes", 
                      std::fs::metadata(&temp_file)
                          .map(|m| m.len())
                          .unwrap_or(0));
            }
            Err(e) => {
                let error_msg = format!("下载失败: {}", e);
                error!("{}", error_msg);
                
                // 发送错误事件
                let _ = window.emit("launcher_update_status", json!({
                    "status": "error",
                    "message": error_msg
                }));
                
                return Err(error_msg);
            }
        }

        // 发送替换文件事件
        let _ = window.emit("launcher_update_status", json!({
            "status": "replacing",
            "message": "正在替换启动器文件..."
        }));

        // 使用 self_update 的文件替换功能
        info!("开始替换启动器文件...");
        
        // 尝试替换文件
        match self_update::self_replace::self_replace(temp_file) {
            Ok(_) => {
                info!("文件替换成功");
            }
            Err(e) => {
                error!("文件替换失败: {}", e);
                return Err(format!("替换文件失败: {}", e));
            }
        }

        // 发送更新完成事件
        let _ = window.emit("launcher_update_status", json!({
            "status": "completed",
            "message": format!("已更新到版本: {}，正在重启...", latest_version),
            "version": latest_version
        }));

        info!("启动器已更新到版本: {}，准备重启...", latest_version);

        // 延迟2秒让用户看到完成消息
        tokio::time::sleep(Duration::from_secs(2)).await;

        // 重启程序
        Self::restart_application(&window).await?;

        Ok(true)
    }

    /// 仅检查是否有更新（不执行更新）
    pub async fn check_only() -> Result<bool, String> {
        info!("仅检查是否有更新...");
        
        let latest_release = Self::get_latest_release().await?;
        let raw_tag = latest_release["tag_name"]
            .as_str()
            .ok_or("无法获取版本号")?;
        
        // 处理版本号：去掉 v 或 V 前缀，并去掉首尾空格
        let latest_version = raw_tag
            .trim()
            .trim_start_matches('v')
            .trim_start_matches('V')
            .trim();

        let needs_update = latest_version != CURRENT_VERSION;
        
        info!("版本检查详情: 原始标签='{}', 处理后最新版本='{}', 当前版本='{}', 需要更新={}", 
              raw_tag, latest_version, CURRENT_VERSION, needs_update);
        
        Ok(needs_update)
    }

    /// 获取最新版本信息
    async fn get_latest_release() -> Result<serde_json::Value, String> {
        let client = reqwest::Client::new();
        let url = get_config_value("LAUNCHER_VERSION_URL");
        
        let response = client
            .get(url)
            .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
            .send()
            .await
            .map_err(|e| format!("请求失败: {}", e))?;

        let release: serde_json::Value = response
            .json()
            .await
            .map_err(|e| format!("解析响应失败: {}", e))?;

        Ok(release)
    }

    /// 使用超时机制下载文件
    async fn download_file_with_timeout(
        url: &str,
        dest_path: &Path,
        window: &Window,
    ) -> Result<(), String> {
        info!("开始下载文件: {} -> {}", url, dest_path.display());
        
        // 创建带超时的客户端
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(300)) // 5分钟超时
            .build()
            .map_err(|e| format!("创建HTTP客户端失败: {}", e))?;

        // 发送下载请求
        let response = client
            .get(url)
            .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
            .send()
            .await
            .map_err(|e| format!("请求下载失败: {}", e))?;

        // 检查响应状态
        if !response.status().is_success() {
            return Err(format!("下载请求失败，状态码: {}", response.status()));
        }

        // 获取文件大小
        let total_size = response.content_length().unwrap_or(0);
        info!("文件总大小: {} bytes", total_size);

        // 创建目标文件
        let mut file = tokio::fs::File::create(dest_path)
            .await
            .map_err(|e| format!("创建文件失败: {}", e))?;

        // 开始下载
        let mut downloaded = 0u64;
        let mut stream = response.bytes_stream();
        
        while let Some(chunk) = stream.next().await {
            let chunk = chunk.map_err(|e| format!("下载数据失败: {}", e))?;
            
            file.write_all(&chunk)
                .await
                .map_err(|e| format!("写入文件失败: {}", e))?;
            
            downloaded += chunk.len() as u64;
            
            // 发送进度更新事件
            if total_size > 0 {
                let progress = (downloaded as f64 / total_size as f64 * 100.0) as u8;
                let _ = window.emit("launcher_update_status", json!({
                    "status": "downloading",
                    "message": format!("正在下载更新文件... ({}%)", progress),
                    "progress": progress,
                    "downloaded": downloaded,
                    "total": total_size
                }));
            }
        }

        // 确保数据写入磁盘
        file.flush().await.map_err(|e| format!("刷新文件失败: {}", e))?;

        info!("下载完成: {} bytes", downloaded);
        Ok(())
    }

    /// 重启应用程序
    async fn restart_application(_window: &Window) -> Result<(), String> {
        info!("开始重启应用程序...");
        
        // 获取当前程序路径
        let current_exe = env::current_exe()
            .map_err(|e| format!("获取当前程序路径失败: {}", e))?;
        
        info!("当前程序路径: {}", current_exe.display());

        // 延迟2秒后重启
        tokio::time::sleep(Duration::from_secs(2)).await;

        // 使用Windows API重启
        #[cfg(windows)]
        {
            Self::restart_with_shell_execute(&current_exe)?;
        }

        #[cfg(not(windows))]
        {
            // 非Windows平台的备用方案
            use std::process::Command;
            match Command::new(&current_exe).spawn() {
                Ok(_) => info!("重启命令已启动"),
                Err(e) => return Err(format!("重启失败: {}", e)),
            }
        }

        // 退出当前进程
        info!("正在退出当前进程...");
        std::process::exit(0);
    }

    /// 使用ShellExecuteW重启应用程序 (Windows专用)
    #[cfg(windows)]
    fn restart_with_shell_execute(exe_path: &std::path::Path) -> Result<(), String> {
        
        // 将路径转换为宽字符串
        let wide_path = U16CString::from_str(exe_path.to_string_lossy().as_ref())
            .map_err(|e| format!("路径转换失败: {}", e))?;
        
        info!("使用ShellExecuteW重启: {}", exe_path.display());
        
        // 调用ShellExecuteW
        unsafe {
            let result = ShellExecuteW(
                None,                           // hwnd
                PCWSTR::null(),                 // lpOperation (默认为"open")
                PCWSTR::from_raw(wide_path.as_ptr()), // lpFile
                PCWSTR::null(),                 // lpParameters
                PCWSTR::null(),                 // lpDirectory
                SW_SHOWNORMAL,                  // nShowCmd
            );
            
            // ShellExecuteW返回值大于32表示成功
            if result.0 as i32 > 32 {
                info!("ShellExecuteW成功启动程序");
                Ok(())
            } else {
                let error_msg = format!("ShellExecuteW失败, 错误代码: {}", result.0);
                error!("{}", error_msg);
                Err(error_msg)
            }
        }
    }

    /// 获取当前版本
    pub fn get_current_version() -> String {
        CURRENT_VERSION.to_string()
    }

    /// 获取更新信息（包括版本和更新日志）
    pub async fn get_update_info() -> Result<serde_json::Value, String> {
        info!("获取更新信息...");
        
        let latest_release = Self::get_latest_release().await?;
        let raw_tag = latest_release["tag_name"]
            .as_str()
            .ok_or("无法获取版本号")?;
        
        // 处理版本号
        let latest_version = raw_tag
            .trim()
            .trim_start_matches('v')
            .trim_start_matches('V')
            .trim();

        // 获取更新日志
        let changelog = latest_release["body"]
            .as_str()
            .unwrap_or("")
            .to_string();

        // 处理更新日志格式
        let formatted_changelog = if changelog.is_empty() {
            "".to_string()
        } else {
            // 简单的Markdown到HTML转换
            let html_changelog = changelog
                .lines()
                .map(|line| {
                    let trimmed = line.trim();
                    if trimmed.starts_with("- ") || trimmed.starts_with("* ") {
                        format!("<p>• {}</p>", &trimmed[2..])
                    } else if trimmed.starts_with("## ") {
                        format!("<h3>{}</h3>", &trimmed[3..])
                    } else if trimmed.starts_with("# ") {
                        format!("<h2>{}</h2>", &trimmed[2..])
                    } else if !trimmed.is_empty() {
                        format!("<p>{}</p>", trimmed)
                    } else {
                        "".to_string()
                    }
                })
                .filter(|line| !line.is_empty())
                .collect::<Vec<_>>()
                .join("");
            
            html_changelog
        };

        let update_info = json!({
            "current_version": CURRENT_VERSION,
            "latest_version": latest_version,
            "needs_update": latest_version != CURRENT_VERSION,
            "changelog": formatted_changelog,
            "raw_changelog": changelog
        });

        info!("更新信息获取成功: 当前版本={}, 最新版本={}, 需要更新={}", 
              CURRENT_VERSION, latest_version, latest_version != CURRENT_VERSION);

        Ok(update_info)
    }
}

// ============================================================================
// Tauri 命令
// ============================================================================

/// 检查启动器更新 - Tauri 命令
#[tauri::command]
pub async fn check_launcher_update_only() -> Result<bool, String> {
    LauncherSelfUpdate::check_only().await
}

/// 检查并执行启动器更新 - Tauri 命令
#[tauri::command]
pub async fn check_and_update_launcher(window: Window) -> Result<bool, String> {
    LauncherSelfUpdate::check_and_update(&window).await
}

/// 获取当前启动器版本 - Tauri 命令
#[tauri::command]
pub async fn get_current_launcher_version() -> Result<String, String> {
    Ok(LauncherSelfUpdate::get_current_version())
}

/// 获取更新信息 - Tauri 命令
#[tauri::command]
pub async fn get_update_info() -> Result<serde_json::Value, String> {
    LauncherSelfUpdate::get_update_info().await
} 