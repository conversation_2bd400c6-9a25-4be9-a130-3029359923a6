use std::path::{Path, PathBuf};
use std::time::Instant;
use std::fs::{self, File};
use std::io::Read;
use std::sync::atomic::{AtomicBool, Ordering};

use log::{error, info};
use rayon::prelude::*;
use reqwest::Client;
use serde_json::{json, Value};
use blake3::Hasher;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Window};


use teralib::config::get_config_value;

// 导入下载模块
use crate::download::{download_files, FileInfo};

// 全局中断标志
static ABORT_FLAG: AtomicBool = AtomicBool::new(false);
// 防止重复发送中断事件的标志
static ABORT_EVENT_SENT: AtomicBool = AtomicBool::new(false);

// ============================================================================
// 数据结构定义
// ============================================================================

// FileInfo 和 ProgressPayload 已移至 download.rs



// ============================================================================
// 更新器结构体
// ============================================================================

/// 游戏更新器
pub struct Updater {
    client: Client,
}

impl Updater {
    /// 创建新的更新器实例
    pub fn new() -> Self {
        Self {
            client: Client::builder()
                .no_proxy()
                .build()
                .expect("Failed to create HTTP client"),
        }
    }

    // ============================================================================
    // 配置和工具方法
    // ============================================================================

    /// 获取哈希文件URL
    fn get_hash_file_url(&self) -> String {
        get_config_value("HASH_FILE_URL")
    }

    /// 计算文件BLAKE3哈希
    fn calculate_file_hash<P: AsRef<Path>>(&self, path: P) -> Result<String, String> {
        let mut file = File::open(path).map_err(|e| format!("Failed to open file: {}", e))?;
        let mut hasher = Hasher::new();
        let mut buffer = [0; 65536]; // 使用更大的缓冲区提高性能

        loop {
            let bytes_read = file.read(&mut buffer).map_err(|e| format!("Failed to read file: {}", e))?;
            if bytes_read == 0 {
                break;
            }
            hasher.update(&buffer[..bytes_read]);
        }

        Ok(hasher.finalize().to_hex().to_string())
    }

    // ============================================================================
    // 服务器文件获取和解析
    // ============================================================================

    /// 从服务器获取哈希文件
    async fn get_server_hash_file(&self) -> Result<Value, String> {
        let url = self.get_hash_file_url();
        
        // 检查是否是数据库文件 (支持 .db3, .db, .bin 后缀)
        if url.ends_with(".db3") || url.ends_with(".db") || url.ends_with(".bin") {
            self.download_and_parse_database(url).await
        } else {
            // 传统的JSON格式
            let res = self.client
                .get(url)
                .send()
                .await
                .map_err(|e| e.to_string())?;
            
            let json: Value = res.json().await.map_err(|e| e.to_string())?;
            Ok(json)
        }
    }

    /// 下载并解析SQLite数据库文件
    async fn download_and_parse_database(&self, url: String) -> Result<Value, String> {
        use std::io::Write;
        use std::time::Duration;
        use tokio::time::sleep;
        
        // 下载数据库文件到临时位置
        let temp_path = std::env::temp_dir().join("server_hash.db3");
        
        let max_retries = 3;
        let mut last_err = None;
        for attempt in 1..=max_retries {
            info!("开始下载数据库文件: {} (第{}/{}次)", url, attempt, max_retries);
            let response_result = self.client
                .get(&url)
                .send()
                .await;
            let response = match response_result {
                Ok(resp) => resp,
                Err(e) => {
                    error!("下载数据库文件失败: {} (第{}/{}次)", e, attempt, max_retries);
                    last_err = Some(format!("下载数据库文件失败: {}", e));
                    if attempt < max_retries {
                        sleep(Duration::from_secs(1)).await;
                        continue;
                    } else {
                        return Err(format!("下载数据库文件失败: {}", e));
                    }
                }
            };
            // 检查HTTP状态码
            if !response.status().is_success() {
                let status = response.status();
                error!("下载数据库文件失败，HTTP状态码: {} (第{}/{})", status, attempt, max_retries);
                last_err = Some(format!("下载数据库文件失败，HTTP状态码: {}", status));
                if attempt < max_retries {
                    sleep(Duration::from_secs(1)).await;
                    continue;
                } else {
                    return Err(format!("下载数据库文件失败，HTTP状态码: {}", status));
                }
            }
            let bytes_result = response.bytes().await;
            let bytes = match bytes_result {
                Ok(b) => b,
                Err(e) => {
                    error!("读取数据库文件内容失败: {} (第{}/{})", e, attempt, max_retries);
                    last_err = Some(format!("读取数据库文件内容失败: {}", e));
                    if attempt < max_retries {
                        sleep(Duration::from_secs(1)).await;
                        continue;
                    } else {
                        return Err(format!("读取数据库文件内容失败: {}", e));
                    }
                }
            };
            // 检查下载的文件大小
            if bytes.is_empty() {
                error!("下载的数据库文件为空 (第{}/{})", attempt, max_retries);
                last_err = Some("下载的数据库文件为空".to_string());
                if attempt < max_retries {
                    sleep(Duration::from_secs(1)).await;
                    continue;
                } else {
                    return Err("下载的数据库文件为空".to_string());
                }
            }
            info!("数据库文件下载完成，大小: {} 字节", bytes.len());
            let mut temp_file = File::create(&temp_path).map_err(|e| {
                error!("创建临时文件失败: {}", e);
                format!("创建临时文件失败: {}", e)
            })?;
            temp_file.write_all(&bytes).map_err(|e| {
                error!("写入临时文件失败: {}", e);
                format!("写入临时文件失败: {}", e)
            })?;
            // 确保文件写入完成
            temp_file.flush().map_err(|e| {
                error!("刷新文件缓冲区失败: {}", e);
                format!("刷新文件缓冲区失败: {}", e)
            })?;
            info!("临时数据库文件创建完成: {:?}", temp_path);
            // 解析数据库文件
            match self.parse_database_file(&temp_path) {
                Ok(data) => {
                    info!("数据库文件解析成功");
                    return Ok(data)
                }
                Err(e) => {
                    error!("数据库文件解析失败: {}", e);
                    // 尝试删除损坏的临时文件
                    if let Err(delete_err) = std::fs::remove_file(&temp_path) {
                        error!("删除损坏的临时文件失败: {}", delete_err);
                    }
                    return Err(e);
                }
            }
        }
        // 理论上不会走到这里
        Err(last_err.unwrap_or_else(|| "未知错误".to_string()))
    }

    /// 解析SQLite数据库文件
    fn parse_database_file(&self, db_path: &Path) -> Result<Value, String> {
        use rusqlite::Connection;
        
        // 检查文件是否存在
        if !db_path.exists() {
            return Err(format!("数据库文件不存在: {:?}", db_path));
        }
        
        // 检查文件大小
        let metadata = std::fs::metadata(db_path).map_err(|e| format!("无法读取文件元数据: {}", e))?;
        if metadata.len() == 0 {
            return Err("数据库文件为空".to_string());
        }
        
        let conn = Connection::open(db_path).map_err(|e| {
            error!("打开数据库失败: {} - 文件路径: {:?}", e, db_path);
            format!("数据库文件损坏或格式错误: {}", e)
        })?;
        
        // 验证数据库完整性
        if let Err(e) = conn.execute_batch("PRAGMA integrity_check") {
            error!("数据库完整性检查失败: {}", e);
            return Err(format!("数据库完整性检查失败: {}", e));
        }
        
        // 先检查表结构，判断是否有download_url字段
        let has_url_column = conn.prepare("SELECT download_url FROM file_info LIMIT 1").is_ok();
        
        let query = if has_url_column {
            "SELECT path, hash, size, download_url FROM file_info ORDER BY path"
        } else {
            "SELECT path, hash, size FROM file_info ORDER BY path"
        };
        
        let mut stmt = conn.prepare(query).map_err(|e| {
            error!("准备SQL语句失败: {}", e);
            format!("数据库表结构错误: {}", e)
        })?;
        
        let file_rows = stmt.query_map([], |row| {
            if has_url_column {
                Ok(json!({
                    "path": row.get::<_, String>(0)?,
                    "hash": row.get::<_, String>(1)?,
                    "size": row.get::<_, u64>(2)?,
                    "url": row.get::<_, String>(3)?
                }))
            } else {
                Ok(json!({
                    "path": row.get::<_, String>(0)?,
                    "hash": row.get::<_, String>(1)?,
                    "size": row.get::<_, u64>(2)?,
                    "url": ""
                }))
            }
        }).map_err(|e| {
            error!("查询数据库失败: {}", e);
            format!("查询数据库失败: {}", e)
        })?;
        
        let mut files = Vec::new();
        for file_result in file_rows {
            files.push(file_result.map_err(|e| e.to_string())?);
        }
        
        info!("成功解析数据库文件，包含 {} 个文件", files.len());
        Ok(json!({
            "files": files
        }))
    }

    // ============================================================================
    // 文件检查逻辑
    // ============================================================================

    /// 获取需要更新的文件列表（更新模式）
    async fn get_files_for_update(&self, _game_path: &Path, server_file_data: &Value, _window: Option<&Window>) -> Result<Vec<FileInfo>, String> {
        info!("开始获取需要更新的文件列表");

        // 1. 读取本地 client.bin 数据库
        let exe_path = std::env::current_exe()
            .map_err(|e| format!("获取可执行文件路径失败: {}", e))?;
        let exe_dir = exe_path.parent()
            .ok_or("无法获取可执行文件目录")?;
        let client_bin_path = exe_dir.join("client.bin");
        
        let local_file_data = if client_bin_path.exists() {
            match self.parse_database_file(&client_bin_path) {
                Ok(data) => data,
                Err(e) => {
                    error!("读取本地client.bin数据库失败: {}", e);
                    // 如果本地数据库无法读取，返回所有服务器文件
                    let files = server_file_data["files"].as_array().ok_or("无效的哈希文件格式")?;
                    return Ok(files.iter().filter_map(|file_info| {
                        // 在遍历时检查中断标志
                        if ABORT_FLAG.load(Ordering::SeqCst) {
                            return None; // 中断当前迭代
                        }
                        let path = file_info["path"].as_str().unwrap_or("");
                        let server_hash = file_info["hash"].as_str().unwrap_or("");
                        let size = file_info["size"].as_u64().unwrap_or(0);
                        let url = file_info["url"].as_str().unwrap_or("").to_string();
                        
                        Some(FileInfo {
                            path: path.to_string(),
                            hash: server_hash.to_string(),
                            size,
                            url,
                        })
                    }).collect());
                }
            }
        } else {
            info!("本地client.bin不存在，需要更新所有文件");
            // 本地数据库不存在，返回所有服务器文件
            let files = server_file_data["files"].as_array().ok_or("无效的哈希文件格式")?;
            return Ok(files.iter().filter_map(|file_info| {
                // 在遍历时检查中断标志
                if ABORT_FLAG.load(Ordering::SeqCst) {
                    return None; // 中断当前迭代
                }
                let path = file_info["path"].as_str().unwrap_or("");
                let server_hash = file_info["hash"].as_str().unwrap_or("");
                let size = file_info["size"].as_u64().unwrap_or(0);
                let url = file_info["url"].as_str().unwrap_or("").to_string();
                
                Some(FileInfo {
                    path: path.to_string(),
                    hash: server_hash.to_string(),
                    size,
                    url,
                })
            }).collect());
        };

        // 2. 创建本地文件的哈希映射 (path -> hash)
        let mut local_hashes = std::collections::HashMap::new();
        if let Some(local_files) = local_file_data["files"].as_array() {
        for file in local_files {
            // 在遍历时检查中断标志
            if ABORT_FLAG.load(Ordering::SeqCst) {
                return Err("更新文件列表获取被用户中断".to_string());
            }
            if let (Some(path), Some(hash)) = (file["path"].as_str(), file["hash"].as_str()) {
                local_hashes.insert(path.to_string(), hash.to_string());
                }
            }
        }

        // 3. 对比服务器文件和本地文件的哈希值
        let files = server_file_data["files"].as_array().ok_or("无效的哈希文件格式")?;
        let files_to_update: Vec<FileInfo> = files.par_iter()
            .filter_map(|file_info| {
                // 在并行迭代器中检查中断标志
                if ABORT_FLAG.load(Ordering::SeqCst) {
                    return None; // 中断当前迭代
                }
                let path = file_info["path"].as_str().unwrap_or("");
                let server_hash = file_info["hash"].as_str().unwrap_or("");
                let size = file_info["size"].as_u64().unwrap_or(0);
                let url = file_info["url"].as_str().unwrap_or("").to_string();

                // 检查本地是否有该文件
                match local_hashes.get(path) {
                    Some(local_hash) => {
                        // 本地有该文件，比较哈希值
                        if local_hash != server_hash {
                            info!("文件哈希不同，需要更新：{} (本地: {}, 服务器: {})", path, local_hash, server_hash);
                            return Some(FileInfo {
                                path: path.to_string(),
                                hash: server_hash.to_string(),
                                size,
                                url,
                            });
                        }
                    }
                    None => {
                        // 本地没有该文件，需要下载
                        info!("本地缺少文件，需要更新：{}", path);
                        return Some(FileInfo {
                            path: path.to_string(),
                            hash: server_hash.to_string(),
                            size,
                            url,
                        });
                    }
                }

                None // 文件存在且哈希相同，不需要更新
            })
            .collect();

        // 再次检查全局中断标志，因为并行操作后可能需要终止整个函数
        if ABORT_FLAG.load(Ordering::SeqCst) {
            return Err("更新文件列表获取被用户中断".to_string());
        }

        info!("更新模式：找到 {} 个需要更新的文件", files_to_update.len());
        Ok(files_to_update)
    }

    /// 获取需要修复的文件列表（修复模式）
    async fn get_files_for_repair(&self, game_path: &Path, server_file_data: &Value, window: Option<&Window>) -> Result<Vec<FileInfo>, String> {
        info!("开始获取需要修复的文件列表");

        let files = server_file_data["files"].as_array().ok_or("无效的哈希文件格式")?;
        let total_files = files.len();

        // 先收集所有文件信息，然后逐个检查并发送进度
        let file_infos: Vec<_> = files.iter()
            .map(|file_info| {
                let path = file_info["path"].as_str().unwrap_or("");
                let server_hash = file_info["hash"].as_str().unwrap_or("");
                let size = file_info["size"].as_u64().unwrap_or(0);
                let url = file_info["url"].as_str().unwrap_or("").to_string();
                (path.to_string(), server_hash.to_string(), size, url)
            })
            .collect();

        let mut files_to_repair = Vec::new();
        let mut checked_files = 0;

        for (path, server_hash, size, url) in file_infos {
            // 在每次文件处理前检查中断标志
            if ABORT_FLAG.load(Ordering::SeqCst) {
                return Err("修复文件列表获取被用户中断".to_string());
            }

            checked_files += 1;
            
            // 发送进度事件
            if let Some(window) = window {
                let progress = (checked_files as f64 / total_files as f64) * 100.0;
                let _ = window.emit("file_check_progress", json!({
                    "progress": progress,
                    "current_file": path,
                    "current_count": checked_files,
                    "total_files": total_files
                }));
            }

            let local_file_path = game_path.join(&path);

            // 修复模式：全面检查文件
                if !local_file_path.exists() {
                info!("文件不存在，需要修复：{}", path);
                files_to_repair.push(FileInfo {
                        path: path.to_string(),
                        hash: server_hash.to_string(),
                        size,
                        url,
                    });
                continue;
                }

            // 检查文件大小
                let metadata = match fs::metadata(&local_file_path) {
                    Ok(m) => m,
                    Err(_) => {
                    info!("无法获取文件元数据，需要修复：{}", path);
                    files_to_repair.push(FileInfo {
                            path: path.to_string(),
                            hash: server_hash.to_string(),
                            size,
                            url,
                        });
                    continue;
                }
            };

                if metadata.len() != size {
                info!("文件大小不匹配，需要修复：{} (本地: {}, 服务器: {})", path, metadata.len(), size);
                files_to_repair.push(FileInfo {
                        path: path.to_string(),
                        hash: server_hash.to_string(),
                        size,
                        url,
                    });
                continue;
            }

            // 检查文件哈希
            match self.calculate_file_hash(&local_file_path) {
                Ok(local_hash) if local_hash != server_hash => {
                    info!("文件哈希不匹配，需要修复：{}", path);
                    files_to_repair.push(FileInfo {
                            path: path.to_string(),
                            hash: server_hash.to_string(),
                            size,
                            url,
                        });
                },
                // 如果哈希匹配，文件正常，不需要修复
                Ok(_) => {},
                // 如果哈希计算本身被中断，也会在这里捕获到 Err 并退出
                Err(e) => {
                    if e.contains("用户中断") {
                        return Err(e); // 传递中断错误
                    }
                    info!("计算文件哈希失败，需要修复：{} - {}", path, e);
                    files_to_repair.push(FileInfo {
                            path: path.to_string(),
                            hash: server_hash.to_string(),
                            size,
                            url,
                        });
                }
            }
        }

        info!("修复模式：找到 {} 个需要修复的文件", files_to_repair.len());
        Ok(files_to_repair)
    }

    // ============================================================================
    // 主要操作接口
    // ============================================================================

    /// 执行文件操作的通用函数 (更新或修复)
    async fn perform_file_operation(
        &self,
        window: Window,
        game_path: PathBuf,
        operation_mode: &str, // "update" or "repair"
    ) -> Result<Vec<FileInfo>, String> {
        // 重置中断标志
        ABORT_FLAG.store(false, Ordering::SeqCst);
        ABORT_EVENT_SENT.store(false, Ordering::SeqCst);
        crate::download::reset_abort_flag();
        
        let start_time = Instant::now();
        info!("开始执行 {} 操作", operation_mode);

        // 从服务器获取最新的文件哈希数据
        let server_file_data = self.get_server_hash_file().await?;
        
        // 检查是否被中断
        if ABORT_FLAG.load(Ordering::SeqCst) {
            info!("{} 操作被用户中断", operation_mode);
            // 注意：这里不发送中断事件，因为abort_patch_command已经发送了
            return Err(format!("{} 操作被用户中断", operation_mode));
        }
        
        let files_to_process = match operation_mode {
            "update" => self.get_files_for_update(&game_path, &server_file_data, Some(&window)).await?,
            "repair" => self.get_files_for_repair(&game_path, &server_file_data, Some(&window)).await?,
            _ => return Err("未知的操作模式".to_string()),
        };

        // 检查是否被中断
        if ABORT_FLAG.load(Ordering::SeqCst) {
            info!("{} 操作被用户中断", operation_mode);
            // 注意：这里不发送中断事件，因为abort_patch_command已经发送了
            return Err(format!("{} 操作被用户中断", operation_mode));
        }

        let total_time = start_time.elapsed();
        info!("{} 检查完成。需要处理的文件: {}", operation_mode, files_to_process.len());

        // 发送完成事件
        let _ = window.emit("file_check_completed", json!({
            "total_files": files_to_process.len(),
            "files_to_update": files_to_process.len(), // 兼容旧字段，实际是需要处理的文件数量
            "total_time_seconds": total_time.as_secs(),
            "mode": operation_mode
        }));

        Ok(files_to_process)
    }

    /// 执行更新操作
    pub async fn perform_update(&self, window: Window, game_path: PathBuf) -> Result<Vec<FileInfo>, String> {
        self.perform_file_operation(window, game_path, "update").await
    }

    /// 执行修复操作
    pub async fn perform_repair(&self, window: Window, game_path: PathBuf) -> Result<Vec<FileInfo>, String> {
        self.perform_file_operation(window, game_path, "repair").await
    }
    

    /// 检查是否需要更新
    pub async fn check_update_required(&self, window: Window, game_path: PathBuf) -> Result<bool, String> {
        match self.perform_update(window, game_path).await {
            Ok(files) => Ok(!files.is_empty()),
            Err(e) => Err(e),
        }
    }

    // ============================================================================
    // 文件下载功能
    // ============================================================================

    /// 下载所有文件（调用 download.rs）
    pub async fn download_all_files(
        &self,
        window: Window,
        files_to_update: Vec<FileInfo>,
        game_path: PathBuf,
    ) -> Result<Vec<u64>, String> {
        let result = download_files(window, files_to_update.clone(), game_path).await;
        
        // 下载成功后，更新本地 client.bin 数据库
        if result.is_ok() {
            if let Err(e) = self.update_local_database(&files_to_update).await {
                error!("更新本地数据库失败: {}", e);
            }
        }
        
        result
    }

    /// 更新本地 client.bin 数据库中的哈希值
    async fn update_local_database(&self, updated_files: &[FileInfo]) -> Result<(), String> {
        use rusqlite::Connection;
        
        let exe_path = std::env::current_exe()
            .map_err(|e| format!("获取可执行文件路径失败: {}", e))?;
        let exe_dir = exe_path.parent()
            .ok_or("无法获取可执行文件目录")?;
        let client_bin_path = exe_dir.join("client.bin");
        
        if !client_bin_path.exists() {
            return Err("本地 client.bin 数据库不存在".to_string());
        }
        
        let mut conn = Connection::open(&client_bin_path).map_err(|e| e.to_string())?;
        
        // 开始事务
        let tx = conn.transaction().map_err(|e| e.to_string())?;
        
        for file_info in updated_files {
            // 更新文件哈希值
            let update_sql = "UPDATE file_info SET hash = ?, size = ? WHERE path = ?";
            tx.execute(update_sql, [
                &file_info.hash,
                &file_info.size.to_string(),
                &file_info.path
            ]).map_err(|e| format!("更新文件 {} 失败: {}", file_info.path, e))?;
            
            info!("已更新本地数据库中的文件: {} -> {}", file_info.path, file_info.hash);
        }
        
        // 提交事务
        tx.commit().map_err(|e| e.to_string())?;
        
        info!("本地数据库更新完成，共更新 {} 个文件", updated_files.len());
        Ok(())
    }

}

// ============================================================================
// 工具函数
// ============================================================================

/// 获取游戏根目录（启动器可执行文件所在目录）
fn get_game_root_path() -> Result<PathBuf, String> {
    let exe_path = std::env::current_exe()
        .map_err(|e| format!("获取可执行文件路径失败: {}", e))?;
    let exe_dir = exe_path.parent()
        .ok_or("无法获取可执行文件目录")?;
    Ok(exe_dir.to_path_buf())
}

// ============================================================================
// Tauri 命令接口
// ============================================================================

/// 获取需要更新的文件列表 - Tauri 命令
#[tauri::command]
pub async fn get_files_to_update_command(window: Window) -> Result<Vec<FileInfo>, String> {
    let game_path = get_game_root_path()?;
    let updater = Updater::new();
    updater.perform_update(window, game_path).await
}

/// 检查是否需要更新 - Tauri 命令
#[tauri::command]
pub async fn check_update_required_command(window: Window) -> Result<bool, String> {
    let game_path = get_game_root_path()?;
    let updater = Updater::new();
    updater.check_update_required(window, game_path).await
}

// 单个文件下载命令已移除，统一使用 download_all_files_command

/// 下载所有文件 - Tauri 命令
#[tauri::command]
pub async fn download_all_files_command(
    window: Window,
    files_to_update: Vec<FileInfo>,
) -> Result<Vec<u64>, String> {
    let game_path = get_game_root_path()?;
    let updater = Updater::new();
    updater.download_all_files(window, files_to_update, game_path).await
}

/// 中断补丁处理命令
#[tauri::command]
pub async fn abort_patch_command(window: Window) -> Result<String, String> {
    info!("收到中断补丁命令");
    
    // 设置中断标志
    ABORT_FLAG.store(true, Ordering::SeqCst);
    
    // 通知下载模块也中断
    crate::download::set_abort_flag(true);
    
    // 防止重复发送中断事件
    if !ABORT_EVENT_SENT.load(Ordering::SeqCst) {
        ABORT_EVENT_SENT.store(true, Ordering::SeqCst);
        // 发送中断结果事件给前端
        let _ = window.emit("patch_result", (1, 0, "", 0, 0)); // patch_result=1 表示中断
    }
    
    Ok("补丁处理已中断".to_string())
}

// ============================================================================
// 补丁检查功能
// ============================================================================

/// 比较两个数据库文件中的哈希信息
fn compare_database_files(local_data: &Value, remote_data: &Value) -> bool {
    // 只保留关键日志，去除无意义的println!
    let local_files = match local_data["files"].as_array() {
        Some(files) => {
            info!("本地数据库包含 {} 个文件", files.len());
            files
        },
        None => {
            error!("本地数据库格式错误：缺少files数组");
            return true; // 格式错误时返回需要更新
        }
    };
    
    let remote_files = match remote_data["files"].as_array() {
        Some(files) => {
            info!("远程数据库包含 {} 个文件", files.len());
            files
        },
        None => {
            error!("远程数据库格式错误：缺少files数组");
            return true; // 格式错误时返回需要更新
        }
    };
    
    // 创建本地文件的哈希映射 (path -> hash)
    let mut local_hashes = std::collections::HashMap::new();
    for file in local_files {
        if let (Some(path), Some(hash)) = (file["path"].as_str(), file["hash"].as_str()) {
            local_hashes.insert(path.to_string(), hash.to_string());
        }
    }
    
    // 检查远程文件是否有差异
    for remote_file in remote_files {
        if let (Some(path), Some(remote_hash)) = (remote_file["path"].as_str(), remote_file["hash"].as_str()) {
            match local_hashes.get(path) {
                Some(local_hash) => {
                    if local_hash != remote_hash {
                        info!("文件哈希不同: {} (本地: {}, 远程: {})", path, local_hash, remote_hash);
                        return true; // 发现差异，需要更新
                    }
                }
                None => {
                    info!("本地缺少文件: {}", path);
                    return true; // 本地缺少文件，需要更新
                }
            }
        }
    }
    
    // 检查本地是否有多余的文件（在远程不存在）
    let mut remote_paths = std::collections::HashSet::new();
    for remote_file in remote_files {
        if let Some(path) = remote_file["path"].as_str() {
            remote_paths.insert(path);
        }
    }
    
    for local_path in local_hashes.keys() {
        if !remote_paths.contains(local_path.as_str()) {
            // 这里可以选择是否认为多余文件需要更新
            // 暂时不认为多余文件需要更新，只处理缺少或哈希不同的文件
        }
    }
    
    info!("所有文件哈希检查完成，无差异");
    false // 没有差异，不需要更新
}

/// 内部的补丁检查逻辑
async fn check_patch_internal(app_handle: &AppHandle) -> Result<String, String> {
    // 去除无意义的println!，保留关键日志
    info!("开始检查补丁状态");
    
    let updater = Updater::new();
    
    // 1. 读取本地 client.bin 数据库 (启动器可执行文件同级目录)
    let exe_path = std::env::current_exe()
        .map_err(|e| format!("获取可执行文件路径失败: {}", e))?;
    let exe_dir = exe_path.parent()
        .ok_or("无法获取可执行文件目录")?;
    let client_bin_path = exe_dir.join("client.bin");
        
    let local_file_data = if client_bin_path.exists() {
        match updater.parse_database_file(&client_bin_path) {
            Ok(data) => data,
            Err(e) => {
                error!("读取本地client.bin数据库失败: {}", e);
                // 如果本地数据库无法读取，返回需要更新
                let _ = app_handle.emit_all("patch_check_result", (3, 0, "", 0, 0));
                return Ok("需要更新".to_string());
            }
        }
    } else {
        info!("本地client.bin不存在，需要更新");
        // 本地数据库不存在，需要更新
        let _ = app_handle.emit_all("patch_check_result", (3, 0, "", 0, 0));
        return Ok("需要更新".to_string());
    };
    
    // 2. 从远程URL下载并读取 server.bin 数据库
    let hash_file_url = updater.get_hash_file_url();
    info!("从URL下载server.bin数据库: {}", hash_file_url);
    
    let remote_file_data = match updater.download_and_parse_database(hash_file_url).await {
        Ok(data) => data,
        Err(e) => {
            error!("下载远程 server.bin 数据库失败: {}", e);
            return Err(e);
        }
    };
    
    // 3. 比较两个数据库中的文件哈希
    let has_differences = compare_database_files(&local_file_data, &remote_file_data);

    if has_differences {
        info!("补丁检查：发现文件差异，需要更新");
        let _ = app_handle.emit_all("patch_check_result", (3, 0, "", 0, 0));
        Ok("需要更新".to_string())
    } else {
        info!("补丁检查：所有文件哈希相同，版本是最新的");
        let _ = app_handle.emit_all("patch_check_result", (2, 0, "", 0, 0));
        Ok("最新版本".to_string())
    }
}

/// 补丁检查命令
#[tauri::command]
pub async fn check_patch_command(app_handle: AppHandle, _window: Window) -> Result<String, String> {
    // 去除无意义的println!，保留关键日志
    info!("check_patch_command 开始执行");
    let result = check_patch_internal(&app_handle).await;
    // 去除无意义的println!，保留关键日志
    info!("check_patch_command 执行完成，结果: {:?} ===", result);
    result
}

/// 执行补丁操作命令 (对应前端的 start_p)
#[tauri::command]
pub async fn start_patch_process_command(
    app_handle: AppHandle,
    window: Window,
    action: i32, // 0=启动前检查, 1=更新, 2=修复
) -> Result<String, String> {
    info!("开始补丁处理，动作: {}", action);
    
    match action {
        1 => {
            // 更新操作
            let game_path = get_game_root_path()?;
            let updater = Updater::new();
            
            // 获取需要更新的文件
            let files_to_update = updater.perform_update(window.clone(), game_path.clone()).await?;
            
            if files_to_update.is_empty() {
                info!("没有需要更新的文件");
                let _ = app_handle.emit_all("patch_result", (2, 0, "", 0, 0)); // 最新版本
                return Ok("没有需要更新的文件".to_string());
            }
            
            // 开始更新
            let _ = app_handle.emit_all("patch_result", (5, 0, "", 0, 0)); // 开始下载
            
            match updater.download_all_files(window, files_to_update, game_path).await {
                Ok(_) => {
                    info!("更新完成");
                    let _ = app_handle.emit_all("patch_result", (2, 0, "", 0, 0)); // 完成
                    Ok("更新完成".to_string())
                }
                Err(e) => {
                    error!("更新失败: {}", e);
                    // 检查是否是用户中断
                    if e.contains("被用户中断") {
                        let _ = app_handle.emit_all("patch_result", (1, 0, "", 0, 0)); // 中断
                    } else {
                        let _ = app_handle.emit_all("patch_result", (0, 1, &e, 0, 0)); // 错误
                    }
                    Err(e)
                }
            }
        }
        2 => {
            // 修复操作
            let game_path = get_game_root_path()?;
            let updater = Updater::new();
            
            // 获取需要修复的文件
            let files_to_repair = updater.perform_repair(window.clone(), game_path.clone()).await?;
            
            if files_to_repair.is_empty() {
                info!("没有需要修复的文件");
                let _ = app_handle.emit_all("patch_result", (2, 0, "", 0, 0)); // 最新版本
                return Ok("没有需要修复的文件".to_string());
            }
            
            // 开始修复
            let _ = app_handle.emit_all("patch_result", (5, 0, "", 0, 0)); // 开始下载
            
            match updater.download_all_files(window, files_to_repair, game_path).await {
                Ok(_) => {
                    info!("修复完成");
                    let _ = app_handle.emit_all("patch_result", (2, 0, "", 0, 0)); // 完成
                    Ok("修复完成".to_string())
                }
                Err(e) => {
                    error!("修复失败: {}", e);
                    // 检查是否是用户中断
                    if e.contains("被用户中断") {
                        let _ = app_handle.emit_all("patch_result", (1, 0, "", 0, 0)); // 中断
                    } else {
                        let _ = app_handle.emit_all("patch_result", (0, 1, &e, 0, 0)); // 错误
                    }
                    Err(e)
                }
            }
        }
        0 => {
            info!("启动前检查（action=0）");
            let game_path = get_game_root_path()?;
            let game_path_str = game_path.to_str().ok_or("无法获取游戏路径")?;
            // 使用精准进程检测
            if crate::utils::is_game_running_precise(game_path_str) {
                let _ = app_handle.emit_all("patch_result", (6, 0, "", 0, 0)); // 6=游戏已在运行
                Ok("游戏已在运行".to_string())
            } else {
                let _ = app_handle.emit_all("patch_result", (2, 0, "", 0, 0)); // 2=可启动
                Ok("启动前检查完成".to_string())
            }
        },
        _ => {
            Err(format!("未知的补丁动作: {}", action))
        }
    }
}

