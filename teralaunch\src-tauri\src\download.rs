use std::path::{Path, PathBuf};
use std::fs::File;
use std::io::Read;
use std::time::{Duration, Instant};
use std::sync::atomic::{AtomicBool, Ordering};

use log::info;
use tokio::fs::File as AsyncFile;
use tokio::io::AsyncWriteExt;
use futures_util::StreamExt;
use reqwest::Client;
use serde::{Serialize, Deserialize};
use tauri::Window;
use blake3::Hasher;

// 全局中断标志
static ABORT_FLAG: AtomicBool = AtomicBool::new(false);

/// 设置中断标志
pub fn set_abort_flag(value: bool) {
    ABORT_FLAG.store(value, Ordering::SeqCst);
}

/// 检查是否被中断
fn is_aborted() -> bool {
    ABORT_FLAG.load(Ordering::SeqCst)
}

/// 重置中断标志
pub fn reset_abort_flag() {
    ABORT_FLAG.store(false, Ordering::SeqCst);
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct FileInfo {
    pub path: String,
    pub hash: String,
    pub size: u64,
    pub url: String,
}

#[derive(Clone, Serialize)]
pub struct ProgressPayload {
    pub file_name: String,
    pub progress: f64,
    pub speed: f64,
    pub downloaded_bytes: u64,
    pub total_bytes: u64,
    pub total_files: usize,
    pub elapsed_time: f64,
    pub current_file_index: usize,
}

/// 计算文件BLAKE3哈希
fn calculate_file_hash<P: AsRef<Path>>(path: P) -> Result<String, String> {
    let mut file = File::open(path).map_err(|e| format!("Failed to open file: {}", e))?;
    let mut hasher = Hasher::new();
    let mut buffer = [0; 65536];
    loop {
        // 检查是否被中断
        if is_aborted() {
            return Err("文件哈希计算被用户中断".to_string());
        }
        let bytes_read = file.read(&mut buffer).map_err(|e| format!("Failed to read file: {}", e))?;
        if bytes_read == 0 {
            break;
        }
        hasher.update(&buffer[..bytes_read]);
    }
    Ok(hasher.finalize().to_hex().to_string())
}

/// 下载文件列表（单线程，顺序下载，带进度事件）
pub async fn download_files(
    window: Window,
    files: Vec<FileInfo>,
    game_path: PathBuf,
) -> Result<Vec<u64>, String> {
    // 重置中断标志
    reset_abort_flag();
    
    let client = Client::builder().no_proxy().build().map_err(|e| e.to_string())?;
    let total_files = files.len();
    let total_size: u64 = files.iter().map(|f| f.size).sum();
    if total_files == 0 {
        info!("没有需要下载的文件");
        let _ = window.emit("download_complete", ());
        return Ok(vec![]);
    }
    let mut downloaded_sizes = Vec::with_capacity(total_files);
    let mut downloaded_size: u64 = 0;
    for (index, file_info) in files.into_iter().enumerate() {
        // 检查是否被中断
        if is_aborted() {
            info!("下载被用户中断");
            // 注意：这里不发送中断事件，因为abort_patch_command已经发送了
            return Err("下载被用户中断".to_string());
        }
        
        let file_path = game_path.join(&file_info.path);
        if let Some(parent) = file_path.parent() {
            tokio::fs::create_dir_all(parent).await.map_err(|e| e.to_string())?;
        }
        let res = client.get(&file_info.url)
            .send()
            .await
            .map_err(|e| e.to_string())?;
        let file_size = res.content_length().unwrap_or(file_info.size);
        let mut file = AsyncFile::create(&file_path).await.map_err(|e| e.to_string())?;
        let mut downloaded: u64 = 0;
        let mut stream = res.bytes_stream();
        let start_time = Instant::now();
        let mut last_update = Instant::now();
        info!("下载文件: {}", file_info.path);
        while let Some(chunk_result) = stream.next().await {
            // 检查是否被中断
            if is_aborted() {
                info!("下载被用户中断");
                // 注意：这里不发送中断事件，因为abort_patch_command已经发送了
                return Err("下载被用户中断".to_string());
            }
            
            let chunk = chunk_result.map_err(|e| e.to_string())?;
            file.write_all(&chunk).await.map_err(|e| e.to_string())?;
            downloaded += chunk.len() as u64;
            let now = Instant::now();
            if now.duration_since(last_update) >= Duration::from_millis(100) || downloaded == file_size {
                let elapsed = now.duration_since(start_time);
                let speed = if elapsed.as_secs() > 0 { downloaded / elapsed.as_secs() } else { downloaded };
                let total_downloaded = downloaded_size + downloaded;
                let progress_payload = ProgressPayload {
                    file_name: file_info.path.clone(),
                    progress: (downloaded as f64 / file_size as f64) * 100.0,
                    speed: speed as f64,
                    downloaded_bytes: total_downloaded,
                    total_bytes: total_size,
                    total_files,
                    elapsed_time: elapsed.as_secs_f64(),
                    current_file_index: index + 1,
                };
                let _ = window.emit("download_progress", &progress_payload);
                last_update = now;
            }
            tokio::time::sleep(Duration::from_millis(1)).await;
        }
        file.flush().await.map_err(|e| e.to_string())?;
        // 验证BLAKE3哈希
        let downloaded_hash = tokio::task::spawn_blocking({
            let file_path = file_path.clone();
            move || calculate_file_hash(&file_path)
        }).await.map_err(|e| e.to_string())??;
        if downloaded_hash != file_info.hash {
            return Err(format!("文件哈希不匹配: {}", file_info.path));
        }
        info!("文件下载完成: {}", file_info.path);
        downloaded_size += file_size;
        downloaded_sizes.push(file_size);
    }
    info!("下载完成，共 {} 个文件", total_files);
    let _ = window.emit("download_complete", ());
    Ok(downloaded_sizes)
} 