# 启动器自更新功能

## 📋 **功能概述**

这个功能让你的启动器能够自动检查和更新自己，使用 `self_update` 库的核心功能：
- ✅ 检查 Gitee releases 的最新版本
- ✅ 自动下载更新文件
- ✅ 安全替换启动器执行文件
- ✅ 最少的代码，最高的效率

## 🚀 **快速开始**

### 1. 后端已就绪
后端代码已经集成完成，提供了3个简单的命令：
- `check_launcher_update_only()` - 仅检查是否有更新
- `check_and_update_launcher()` - 检查并执行更新
- `get_current_launcher_version()` - 获取当前版本

### 2. 前端集成
将 `launcher_self_update_simple.js` 添加到你的启动器项目中：

```html
<!-- 在你的 HTML 文件中 -->
<script src="launcher_self_update_simple.js"></script>
```

### 3. 添加更新按钮
```html
<button onclick="checkLauncherUpdate()">检查更新</button>
<button onclick="showVersionInfo()">显示版本</button>
```

### 4. 在 Gitee 发布版本
1. 访问：`https://gitee.com/KVPlay/teralauncher`
2. 点击"发行版" → "创建发行版"
3. 标签：`v1.0.0`（重要：必须以 v 开头）
4. 上传编译好的 `.exe` 文件

## ⚙️ **配置**

### 版本号管理
- **Cargo.toml**：`version = "1.0.0"`（不带 v）
- **Gitee Release**：`v1.0.0`（带 v）

### 自定义仓库
如果你的仓库不是 `KVPlay/teralauncher`，修改 `launcher_self_update.rs` 中的 URL：

```rust
let url = "https://gitee.com/api/v5/repos/你的用户名/你的仓库名/releases/latest";
```

## 🔧 **API 使用**

### JavaScript API
```javascript
// 检查更新
const needsUpdate = await LauncherSelfUpdate.checkUpdate();

// 获取版本
const version = await LauncherSelfUpdate.getCurrentVersion();

// 执行更新
const updated = await LauncherSelfUpdate.performUpdate();
```

### 事件监听
```javascript
// 监听更新状态
window.__TAURI__.event.listen('launcher_update_status', (event) => {
    console.log('更新状态:', event.payload);
});
```

## 📦 **发布流程**

1. **更新版本号**
   ```toml
   # Cargo.toml
   version = "1.0.1"
   ```

2. **编译项目**
   ```bash
   cd tera-launcher/teralaunch
   npm run tauri build
   ```

3. **在 Gitee 创建 Release**
   - 标签：`v1.0.1`
   - 上传：`src-tauri/target/release/teralaunch.exe`

4. **用户自动更新**
   - 启动器会自动检查更新
   - 用户确认后一键更新

## 🎯 **为什么这样设计？**

### 简洁性
- 只有 **80 行** Rust 代码
- 只有 **150 行** JavaScript 代码
- 使用 `self_update` 库的核心功能

### 职责分离
- `updater.rs` → 游戏文件更新
- `launcher_self_update.rs` → 启动器自更新
- 功能独立，互不干扰

### 库的正确使用
```rust
// 使用 self_update 的下载功能
self_update::Download::from_url(download_url)
    .download_to(&temp_file)?;

// 使用 self_update 的替换功能
self_update::self_replace::self_replace(temp_file)?;
```

## 🔍 **测试**

### 本地测试
```bash
# 编译当前版本
npm run tauri build

# 创建测试版本
cp target/release/teralaunch.exe teralaunch_v1.0.0.exe

# 更新版本号到 1.0.1
# 在 Gitee 发布 v1.0.1
# 运行 teralaunch_v1.0.0.exe 测试更新
```

### 调试技巧
```javascript
// 在浏览器控制台
console.log('当前版本:', await LauncherSelfUpdate.getCurrentVersion());
console.log('需要更新:', await LauncherSelfUpdate.checkUpdate());
```

## 🚨 **注意事项**

1. **权限问题**：可能需要管理员权限运行
2. **杀毒软件**：可能需要添加白名单
3. **网络连接**：确保能访问 Gitee API
4. **文件名**：必须是 `.exe` 后缀

## 🎉 **完成！**

现在你有了一个简洁、高效的启动器自更新功能！

- 🔄 **自动检查**更新
- 📥 **一键下载**新版本
- 🔄 **自动替换**启动器
- 🚀 **重启应用**完成更新

用户体验：**发现新版本 → 点击更新 → 自动重启 → 完成** 