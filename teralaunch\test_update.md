# 自更新功能测试指南

## 🧪 测试流程

### 1. 准备测试环境

1. **编译当前版本**
   ```bash
   cd tera-launcher/teralaunch
   npm run tauri build
   ```

2. **复制编译好的文件**
   ```bash
   # 编译后的文件在：
   # src-tauri/target/release/teralaunch.exe
   
   # 复制到测试目录
   cp src-tauri/target/release/teralaunch.exe test_v1.0.0.exe
   ```

### 2. 创建测试 Release

1. **在 Gitee 创建 v1.0.1 版本**
   - 标签：`v1.0.1`
   - 标题：`TerraLauncher v1.0.1 测试版`
   - 内容：`测试自更新功能`
   - 上传：`test_v1.0.1.exe`

2. **运行 v1.0.0 版本**
   ```bash
   # 运行旧版本
   ./test_v1.0.0.exe
   ```

3. **检查更新**
   - 启动器会自动检查更新
   - 或者手动点击"检查更新"按钮

### 3. 测试场景

#### 场景1：正常更新
- 当前版本：1.0.0
- 服务器版本：1.0.1
- 预期结果：显示更新对话框

#### 场景2：已是最新版本
- 当前版本：1.0.1
- 服务器版本：1.0.1
- 预期结果：显示"已是最新版本"

#### 场景3：网络错误
- 断开网络连接
- 点击检查更新
- 预期结果：显示"检查更新失败"

### 4. 调试技巧

1. **查看控制台日志**
   ```javascript
   // 在浏览器开发者工具的控制台查看
   console.log('当前版本:', await LauncherSelfUpdate.getCurrentVersion());
   console.log('更新状态:', LauncherSelfUpdate.getUpdateStatus());
   ```

2. **手动触发更新**
   ```javascript
   // 在控制台手动测试
   LauncherSelfUpdate.manualCheckUpdate();
   ```

3. **模拟 Gitee API 响应**
   ```javascript
   // 测试 Gitee API 是否正常
   fetch('https://gitee.com/api/v5/repos/KVPlay/teralauncher/releases/latest')
     .then(r => r.json())
     .then(data => console.log('最新版本:', data.tag_name));
   ```

## 🔧 常见问题解决

### 问题1：检查更新失败
```
错误：检查更新失败: 请求失败
```

**解决方案**：
1. 检查网络连接
2. 确认 Gitee 仓库是公开的
3. 确认仓库路径正确：`KVPlay/teralauncher`

### 问题2：找不到更新文件
```
错误：找不到可用的更新文件
```

**解决方案**：
1. 确认 Release 中上传了 `.exe` 文件
2. 检查文件名是否以 `.exe` 结尾
3. 确认 Release 不是 draft 状态

### 问题3：版本比较错误
```
错误：当前版本格式错误
```

**解决方案**：
1. 确认版本号格式：`1.0.0`（不要包含 `v` 前缀）
2. 检查 Cargo.toml 中的版本号
3. 确认 Gitee Release 标签格式：`v1.0.0`

### 问题4：文件替换失败
```
错误：替换启动器失败
```

**解决方案**：
1. 确认启动器有写入权限
2. 关闭杀毒软件的实时保护
3. 以管理员身份运行启动器

## 📝 测试检查清单

- [ ] 编译成功，无错误
- [ ] 启动器正常运行
- [ ] 自动检查更新功能工作
- [ ] 手动检查更新功能工作
- [ ] 更新对话框显示正常
- [ ] 版本信息显示正确
- [ ] 更新日志显示正常
- [ ] 下载进度显示正常
- [ ] 文件替换成功
- [ ] 启动器自动重启
- [ ] 错误处理正常
- [ ] 网络异常处理正常

## 🚀 部署前最终检查

1. **版本号一致性**
   - Cargo.toml 中的版本号
   - Gitee Release 标签版本号（去掉 v 前缀）

2. **文件完整性**
   - 编译后的 exe 文件可以正常运行
   - 文件大小合理（不要太大或太小）

3. **功能验证**
   - 在不同的 Windows 版本测试
   - 在有无网络的情况下测试
   - 测试多次连续更新

4. **用户体验**
   - 更新提示清晰明确
   - 进度显示准确
   - 错误信息友好

## 🎯 发布流程建议

1. **开发版本**：`v1.0.0-dev`
2. **测试版本**：`v1.0.0-beta`
3. **正式版本**：`v1.0.0`
4. **修复版本**：`v1.0.1`
5. **功能版本**：`v1.1.0`
6. **重大版本**：`v2.0.0`

每个版本都要经过完整测试后才能发布！ 