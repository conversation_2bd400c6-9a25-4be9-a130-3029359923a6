use log::{info, warn, error};
use std::path::Path;
use std::process::Command;
use std::time::Duration;
use tokio::io::AsyncWriteExt;
use futures_util::StreamExt;
use tempfile::TempDir;

use windows::{
    core::PCWSTR,
    Win32::{
        Foundation::{HWND, LPARAM, LRESULT, WPARAM, RECT, TRUE, COLORREF},
        UI::WindowsAndMessaging::{
            CreateWindowExW, DefWindowProcW, DestroyWindow, LoadCursorW, RegisterClassW, SetTimer, 
            KillTimer, ShowWindow, GetSystemMetrics, MessageBoxW, GetClientRect,
            WNDCLASSW, WM_PAINT, WM_TIMER, WM_CLOSE, WM_DESTROY, WS_OVERLAPPED, WS_CAPTION, WS_SYSMENU, 
            SW_SHOW, CS_HREDRAW, CS_VREDRAW, IDC_ARROW, <PERSON>_C<PERSON>SC<PERSON><PERSON>, SM_CYSCRE<PERSON>, MB_OK, MB_ICONERROR, 
            MB_TOPMOST
        },
        Graphics::Gdi::{
            BeginPaint, End<PERSON>aint, TextOutW, GetStockObject, WHITE_BRUSH, FillRect, CreateFontW,
            SelectObject, SetTextColor, SetBkMode, DeleteObject, TRANSPARENT, PAINTSTRUCT,
            FW_NORMAL, DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY,
            DEFAULT_PITCH, FF_DONTCARE, InvalidateRect, UpdateWindow
        },
        System::LibraryLoader::GetModuleHandleW,
        System::Com::CoTaskMemFree,
    },
};
use widestring::U16CString;
use std::sync::{Arc, Mutex, mpsc};
use std::thread;

#[cfg(windows)]
use webview2_sys::GetAvailableCoreWebView2BrowserVersionString;

// WebView2运行时下载URL
const WEBVIEW2_DOWNLOAD_URL: &str = "https://go.microsoft.com/fwlink/p/?LinkId=2124703";

/// 进度窗口管理器
static mut PROGRESS_WINDOW: Option<ProgressWindow> = None;

/// 当前进度消息
static mut CURRENT_MESSAGE: Option<Arc<Mutex<String>>> = None;

/// 进度窗口结构
struct ProgressWindow {
    hwnd: HWND,
    message_sender: mpsc::Sender<String>,
}

impl ProgressWindow {
    fn new() -> Self {
        let current_message = Arc::new(Mutex::new("正在启动 TERA Launcher...".to_string()));
        let (message_sender, message_receiver) = mpsc::channel();
        
        // 设置全局消息
        unsafe {
            let msg_ptr = std::ptr::addr_of_mut!(CURRENT_MESSAGE);
            *msg_ptr = Some(current_message.clone());
        }
        
        // 在单独线程中创建窗口
        let hwnd_sender = mpsc::channel();
        let hwnd_sender_clone = hwnd_sender.0.clone();
        let current_message_for_thread = current_message.clone();
        
        thread::spawn(move || {
            unsafe {
                let hinstance = GetModuleHandleW(None).unwrap();
                let class_name = U16CString::from_str("TeraLauncherProgress").unwrap();
                
                // 注册窗口类
                let wc = WNDCLASSW {
                    lpfnWndProc: Some(Self::window_proc),
                    hInstance: hinstance.into(),
                    lpszClassName: PCWSTR::from_raw(class_name.as_ptr()),
                    hCursor: LoadCursorW(None, IDC_ARROW).unwrap(),
                    hbrBackground: windows::Win32::Graphics::Gdi::HBRUSH(GetStockObject(WHITE_BRUSH).0),
                    style: CS_HREDRAW | CS_VREDRAW,
                    ..Default::default()
                };
                
                RegisterClassW(&wc);
                
                // 获取屏幕尺寸并计算居中位置
                let screen_width = GetSystemMetrics(SM_CXSCREEN);
                let screen_height = GetSystemMetrics(SM_CYSCREEN);
                let window_width = 450;
                let window_height = 150;
                let x = (screen_width - window_width) / 2;
                let y = (screen_height - window_height) / 2;
                
                // 创建窗口
                let hwnd = CreateWindowExW(
                    Default::default(),
                    PCWSTR::from_raw(class_name.as_ptr()),
                    PCWSTR::from_raw(U16CString::from_str("TERA Launcher - WebView2安装").unwrap().as_ptr()),
                    WS_OVERLAPPED | WS_CAPTION | WS_SYSMENU,
                    x, y, window_width, window_height,
                    None, None, hinstance, None,
                );
                
                // 发送窗口句柄
                hwnd_sender_clone.send(hwnd).unwrap();
                
                ShowWindow(hwnd, SW_SHOW);
                
                // 设置定时器，每500毫秒重绘一次窗口
                SetTimer(hwnd, 1, 500, None);
                
                // 处理消息更新
                thread::spawn(move || {
                    while let Ok(new_message) = message_receiver.recv() {
                        *current_message_for_thread.lock().unwrap() = new_message;
                    }
                });
                
                // 消息循环
                let mut msg = std::mem::zeroed();
                while windows::Win32::UI::WindowsAndMessaging::GetMessageW(&mut msg, None, 0, 0).as_bool() {
                    windows::Win32::UI::WindowsAndMessaging::TranslateMessage(&msg);
                    windows::Win32::UI::WindowsAndMessaging::DispatchMessageW(&msg);
                }
            }
        });
        
        // 等待窗口创建完成
        let hwnd = hwnd_sender.1.recv().unwrap();
        
        Self {
            hwnd,
            message_sender,
        }
    }
    
    fn update_message(&mut self, message: &str) {
        let _ = self.message_sender.send(message.to_string());
    }
    
    fn close(&self) {
        unsafe {
            if self.hwnd.0 != 0 {
                let _ = DestroyWindow(self.hwnd);
            }
            // 清理全局消息
            let msg_ptr = std::ptr::addr_of_mut!(CURRENT_MESSAGE);
            *msg_ptr = None;
        }
    }
    
    unsafe extern "system" fn window_proc(
        hwnd: HWND,
        msg: u32,
        wparam: WPARAM,
        lparam: LPARAM,
    ) -> LRESULT {
        match msg {
            WM_PAINT => {
                let mut ps = PAINTSTRUCT::default();
                let hdc = BeginPaint(hwnd, &mut ps);
                
                // 清空窗口背景
                let mut rect = RECT::default();
                let _ = GetClientRect(hwnd, &mut rect);
                FillRect(hdc, &rect, windows::Win32::Graphics::Gdi::HBRUSH(GetStockObject(WHITE_BRUSH).0));
                
                // 设置字体
                let hfont = CreateFontW(
                    16, 0, 0, 0,
                    FW_NORMAL.0 as i32,
                    0, 0, 0,
                    DEFAULT_CHARSET.0 as u32,
                    OUT_DEFAULT_PRECIS.0 as u32,
                    CLIP_DEFAULT_PRECIS.0 as u32,
                    DEFAULT_QUALITY.0 as u32,
                    (DEFAULT_PITCH.0 | FF_DONTCARE.0) as u32,
                    PCWSTR::from_raw(U16CString::from_str("Microsoft YaHei").unwrap().as_ptr()),
                );
                SelectObject(hdc, hfont);
                
                // 设置文本颜色
                SetTextColor(hdc, COLORREF(0x000000)); // 黑色
                SetBkMode(hdc, TRANSPARENT);
                
                // 显示标题
                let title = U16CString::from_str("WebView2 运行时安装").unwrap();
                TextOutW(hdc, 20, 20, title.as_slice());
                
                // 显示当前进度消息
                let current_msg = {
                    let ptr = std::ptr::addr_of!(CURRENT_MESSAGE);
                    if let Some(msg_arc) = (*ptr).as_ref() {
                        msg_arc.lock().unwrap().clone()
                    } else {
                        "正在初始化...".to_string()
                    }
                };
                
                let message = U16CString::from_str(&current_msg).unwrap();
                TextOutW(hdc, 20, 50, message.as_slice());
                
                let sub_message = U16CString::from_str("请稍候，这可能需要几分钟时间...").unwrap();
                TextOutW(hdc, 20, 80, sub_message.as_slice());
                
                // 显示进度条样式的提示
                let progress_hint = U16CString::from_str("■■■■■■■■■■").unwrap();
                TextOutW(hdc, 20, 110, progress_hint.as_slice());
                
                // 清理资源
                DeleteObject(hfont);
                EndPaint(hwnd, &ps);
                LRESULT(0)
            }
            WM_TIMER => {
                // 定时器消息，使用InvalidateRect来刷新窗口
                InvalidateRect(hwnd, None, TRUE);
                UpdateWindow(hwnd);
                LRESULT(0)
            }
            WM_CLOSE => {
                let _ = KillTimer(hwnd, 1);
                let _ = DestroyWindow(hwnd);
                LRESULT(0)
            }
            WM_DESTROY => {
                let _ = KillTimer(hwnd, 1);
                windows::Win32::UI::WindowsAndMessaging::PostQuitMessage(0);
                LRESULT(0)
            }
            _ => DefWindowProcW(hwnd, msg, wparam, lparam),
        }
    }
}

/// 显示进度通知
pub fn show_progress_notification(message: &str) {
    // 控制台输出（debug版本可见）
    println!("TERA Launcher: {}", message);
    
    // 日志输出
    info!("[启动进度] {}", message);
    
    #[cfg(windows)]
    {
        unsafe {
            let window_ptr = std::ptr::addr_of_mut!(PROGRESS_WINDOW);
            if (*window_ptr).is_none() {
                *window_ptr = Some(ProgressWindow::new());
            }
            
            if let Some(window) = (*window_ptr).as_mut() {
                window.update_message(message);
            }
        }
    }
}

/// 关闭进度窗口
pub fn close_progress_window() {
    #[cfg(windows)]
    {
        unsafe {
            let window_ptr = std::ptr::addr_of_mut!(PROGRESS_WINDOW);
            if let Some(window) = (*window_ptr).as_ref() {
                // 发送WM_CLOSE消息到窗口，启动正常的关闭流程
                let _ = windows::Win32::UI::WindowsAndMessaging::PostMessageW(
                    window.hwnd, 
                    WM_CLOSE, 
                    WPARAM(0), 
                    LPARAM(0)
                );
                
                // 等待窗口关闭
                std::thread::sleep(std::time::Duration::from_millis(200));
                
                // 然后再直接销毁窗口进行双重保险
                window.close();
                *window_ptr = None;
                
                // 确保窗口被清理
                std::thread::sleep(std::time::Duration::from_millis(100));
                info!("进度窗口已关闭");
            }
        }
    }
}

/// 显示启动完成通知（仅在有进度窗口时使用）
#[allow(dead_code)]
pub fn show_startup_complete_notification() {
    #[cfg(windows)]
    {
        unsafe {
            let window_ptr = std::ptr::addr_of!(PROGRESS_WINDOW);
            if (*window_ptr).is_some() {
                show_progress_notification("启动完成，正在打开应用界面...");
                
                // 延迟关闭进度窗口
                thread::spawn(|| {
                    std::thread::sleep(Duration::from_secs(2));
                    close_progress_window();
                });
            }
        }
    }
}

/// 显示错误通知
pub fn show_error_notification(error: &str) {
    #[cfg(windows)]
    {
        unsafe {
            let title = U16CString::from_str("TERA Launcher - 错误").unwrap();
            let content = U16CString::from_str(format!("发生错误:\n\n{}", error)).unwrap();
            
            MessageBoxW(
                None,
                PCWSTR::from_raw(content.as_ptr()),
                PCWSTR::from_raw(title.as_ptr()),
                MB_OK | MB_ICONERROR | MB_TOPMOST,
            );
        }
    }
    
    // 关闭进度窗口
    close_progress_window();
}

pub struct WebView2Installer;

impl WebView2Installer {
    /// 检查WebView2运行时是否已安装
    pub fn is_webview2_installed() -> bool {
        #[cfg(windows)]
        {
            info!("开始检查WebView2运行时是否已安装...");
            
            // 使用官方推荐的API检查WebView2运行时
            let result = Self::check_webview2_with_official_api();
            
            if result {
                info!("WebView2运行时检查结果: 已安装");
            } else {
                warn!("WebView2运行时检查结果: 未检测到");
            }
            
            result
        }
        
        #[cfg(not(windows))]
        {
            // 非Windows平台默认返回true
            true
        }
    }
    
    /// 使用官方API检查WebView2运行时
    #[cfg(windows)]
    fn check_webview2_with_official_api() -> bool {
        info!("使用官方API检查WebView2运行时...");
        
        unsafe {
            let mut version_string = std::ptr::null_mut();
            let hr = GetAvailableCoreWebView2BrowserVersionString(std::ptr::null(), &mut version_string);
            
            if hr == 0 {  // S_OK
                if !version_string.is_null() {
                    // 转换版本字符串为Rust String
                    let version_len = (0..).take_while(|&i| *version_string.offset(i) != 0).count();
                    let version_slice = std::slice::from_raw_parts(version_string, version_len);
                    let version = String::from_utf16_lossy(version_slice);
                    
                    // 释放字符串内存
                    CoTaskMemFree(Some(version_string as *mut _));
                    
                    info!("找到WebView2运行时版本: {}", version);
                    true
                } else {
                    warn!("GetAvailableCoreWebView2BrowserVersionString返回成功但版本字符串为空");
                    false
                }
            } else {
                warn!("GetAvailableCoreWebView2BrowserVersionString失败，错误代码: 0x{:x}", hr as u32);
                false
            }
        }
    }
    

    

    

    
    /// 检查并安装WebView2运行时
    pub async fn check_and_install_webview2() -> Result<(), String> {
        show_progress_notification("正在初始化WebView2安装...");
        
        // 延迟一下让窗口有时间显示
        tokio::time::sleep(Duration::from_millis(500)).await;
        
        show_progress_notification("正在检查WebView2运行时...");
        
        if Self::is_webview2_installed() {
            show_progress_notification("WebView2运行时已安装");
            // 延迟一下让用户看到消息
            tokio::time::sleep(Duration::from_secs(1)).await;
            return Ok(());
        }
        
        show_progress_notification("WebView2运行时未找到，开始下载安装包...");
        
        // 下载WebView2安装包
        let temp_dir = TempDir::new().map_err(|e| format!("创建临时目录失败: {}", e))?;
        let installer_path = temp_dir.path().join("MicrosoftEdgeWebview2Setup.exe");
        
        show_progress_notification("正在下载WebView2安装包，请稍候...");
        Self::download_webview2_installer(&installer_path).await?;
        
        show_progress_notification("下载完成，正在安装WebView2运行时...");
        Self::install_webview2_silently(&installer_path).await?;
        
        show_progress_notification("WebView2安装完成，正在启动应用...");
        
        // 延迟一下让用户看到完成消息
        tokio::time::sleep(Duration::from_secs(2)).await;
        
        // 关闭进度窗口
        info!("准备关闭进度窗口...");
        close_progress_window();
        
        // 再次延迟一下确保窗口完全关闭
        tokio::time::sleep(Duration::from_millis(300)).await;
        
        Ok(())
    }
    
    /// 诊断WebView2安装状态（用于调试）
    pub fn diagnose_webview2() {
        info!("=== WebView2 诊断信息 ===");
        
        #[cfg(windows)]
        {
            info!("操作系统: Windows");
            
            // 使用官方API检查
            info!("--- 官方API检查 ---");
            unsafe {
                let mut version_string = std::ptr::null_mut();
                let hr = GetAvailableCoreWebView2BrowserVersionString(std::ptr::null(), &mut version_string);
                
                if hr == 0 && !version_string.is_null() {
                    let version_len = (0..).take_while(|&i| *version_string.offset(i) != 0).count();
                    let version_slice = std::slice::from_raw_parts(version_string, version_len);
                    let version = String::from_utf16_lossy(version_slice);
                    
                    CoTaskMemFree(Some(version_string as *mut _));
                    
                    info!("官方API检查结果: 成功，版本: {}", version);
                } else {
                    info!("官方API检查结果: 失败，错误代码: 0x{:x}", hr as u32);
                }
            }
        }
        
        #[cfg(not(windows))]
        {
            info!("操作系统: 非Windows，跳过WebView2检查");
        }
        
        info!("=== 诊断完成 ===");
    }
    
    /// 下载WebView2安装包
    async fn download_webview2_installer(dest_path: &Path) -> Result<(), String> {
        info!("下载WebView2安装包到: {}", dest_path.display());
        
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(300)) // 5分钟超时
            .build()
            .map_err(|e| format!("创建HTTP客户端失败: {}", e))?;
        
        let response = client
            .get(WEBVIEW2_DOWNLOAD_URL)
            .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
            .send()
            .await
            .map_err(|e| format!("下载请求失败: {}", e))?;
        
        if !response.status().is_success() {
            return Err(format!("下载失败，状态码: {}", response.status()));
        }
        
        let total_size = response.content_length().unwrap_or(0);
        info!("WebView2安装包大小: {} bytes", total_size);
        
        let mut file = tokio::fs::File::create(dest_path)
            .await
            .map_err(|e| format!("创建文件失败: {}", e))?;
        
        let mut downloaded = 0u64;
        let mut stream = response.bytes_stream();
        let mut last_progress = 0u8;
        
        while let Some(chunk) = stream.next().await {
            let chunk = chunk.map_err(|e| format!("下载数据失败: {}", e))?;
            
            file.write_all(&chunk)
                .await
                .map_err(|e| format!("写入文件失败: {}", e))?;
            
            downloaded += chunk.len() as u64;
            
            if total_size > 0 {
                let progress = (downloaded as f64 / total_size as f64 * 100.0) as u8;
                // 更频繁的进度更新，每10%显示一次
                if progress != last_progress && progress % 10 == 0 {
                    show_progress_notification(&format!("正在下载WebView2安装包... {}%", progress));
                    last_progress = progress;
                }
            }
        }
        
        file.flush().await.map_err(|e| format!("刷新文件失败: {}", e))?;
        info!("WebView2安装包下载完成: {} bytes", downloaded);
        
        Ok(())
    }
    
    /// 静默安装WebView2运行时
    async fn install_webview2_silently(installer_path: &Path) -> Result<(), String> {
        info!("开始静默安装WebView2运行时...");
        
        show_progress_notification("正在初始化WebView2安装程序...");
        
        let mut cmd = Command::new(installer_path);
        cmd.args(["/silent", "/install"]);
        
        // 在后台监控安装进度
        let progress_handle = tokio::spawn(async {
            for i in 0..=100 {
                tokio::time::sleep(Duration::from_millis(200)).await;
                match i {
                    0..=20 => {
                        if i % 5 == 0 {
                            show_progress_notification("正在准备安装WebView2运行时...");
                        }
                    }
                    21..=60 => {
                        if i % 10 == 0 {
                            show_progress_notification("正在安装WebView2运行时组件...");
                        }
                    }
                    61..=90 => {
                        if i % 10 == 0 {
                            show_progress_notification("正在配置WebView2运行时...");
                        }
                    }
                    91..=100 => {
                        if i % 5 == 0 {
                            show_progress_notification("正在完成WebView2运行时安装...");
                        }
                    }
                    _ => {}
                }
            }
        });
        
        // 设置超时时间
        let output = tokio::time::timeout(
            Duration::from_secs(300), // 5分钟超时
            tokio::process::Command::from(cmd).output(),
        )
        .await
        .map_err(|_| "WebView2安装超时".to_string())?
        .map_err(|e| format!("执行安装程序失败: {}", e))?;
        
        // 停止进度监控
        progress_handle.abort();
        
        if output.status.success() {
            info!("WebView2运行时安装成功");
            
            show_progress_notification("WebView2运行时安装成功，正在验证...");
            
            // 等待一段时间确保安装完成
            tokio::time::sleep(Duration::from_secs(3)).await;
            
            // 再次检查是否安装成功
            if Self::is_webview2_installed() {
                info!("WebView2运行时安装验证成功");
                Ok(())
            } else {
                Err("WebView2运行时安装验证失败".to_string())
            }
        } else {
            let stderr = String::from_utf8_lossy(&output.stderr);
            let stdout = String::from_utf8_lossy(&output.stdout);
            
            error!("WebView2安装失败:");
            error!("退出代码: {}", output.status.code().unwrap_or(-1));
            error!("标准输出: {}", stdout);
            error!("标准错误: {}", stderr);
            
            Err(format!("WebView2安装失败，退出代码: {}", output.status.code().unwrap_or(-1)))
        }
    }
    
    /// 获取WebView2运行时版本信息
    pub fn get_webview2_version() -> Option<String> {
        #[cfg(windows)]
        {
            Self::get_webview2_version_with_official_api()
        }
        
        #[cfg(not(windows))]
        {
            None
        }
    }
    
    /// 使用官方API获取WebView2版本
    #[cfg(windows)]
    fn get_webview2_version_with_official_api() -> Option<String> {
        unsafe {
            let mut version_string = std::ptr::null_mut();
            let hr = GetAvailableCoreWebView2BrowserVersionString(std::ptr::null(), &mut version_string);
            
            if hr == 0 && !version_string.is_null() {
                // 转换版本字符串为Rust String
                let version_len = (0..).take_while(|&i| *version_string.offset(i) != 0).count();
                let version_slice = std::slice::from_raw_parts(version_string, version_len);
                let version = String::from_utf16_lossy(version_slice);
                
                // 释放字符串内存
                CoTaskMemFree(Some(version_string as *mut _));
                
                Some(version)
            } else {
                None
            }
        }
    }
    

    

}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_webview2_detection() {
        // 测试WebView2检测功能
        let is_installed = WebView2Installer::is_webview2_installed();
        println!("WebView2 已安装: {}", is_installed);
        
        // 获取版本信息
        if let Some(version) = WebView2Installer::get_webview2_version() {
            println!("WebView2 版本: {}", version);
        } else {
            println!("无法获取WebView2版本");
        }
        
        // 运行诊断
        WebView2Installer::diagnose_webview2();
    }
}

 