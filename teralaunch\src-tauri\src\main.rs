#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::{Manager, Window};
use serde::{Deserialize, Serialize};
use teralib::{get_game_status_receiver, run_game, reset_global_state};
use sysinfo::{SystemExt, ProcessExt, PidExt};
use log::{error, info};
use std::env;

// 导入更新器模块
mod updater;
mod download;
use updater::{
    check_patch_command, start_patch_process_command, 
    get_files_to_update_command, check_update_required_command,
    download_all_files_command, abort_patch_command
};

// 导入启动器自更新模块
mod launcher_self_update;
use launcher_self_update::{
    check_launcher_update_only, check_and_update_launcher, 
    get_current_launcher_version, get_update_info
};

// 导入WebView2安装器模块
mod webview2_installer;

mod utils;

// 插件导入（已弃用 tauri-plugin-single-instance，改用 single_instance）
use single_instance::SingleInstance;

// 窗口大小结构
#[derive(Debug, Serialize, Deserialize)]
struct WindowSize {
    width: f64,
    height: f64,
}

// 全局认证信息
#[derive(Default)]
struct GlobalAuthInfo {
    character_count: String,
    user_no: i32,
    user_name: String,
    auth_key: String,
}

lazy_static::lazy_static! {
    static ref GLOBAL_AUTH_INFO: std::sync::RwLock<GlobalAuthInfo> = std::sync::RwLock::new(GlobalAuthInfo::default());
}

#[derive(Deserialize)]
struct AuthInfoParams {
    #[serde(rename = "userNo")]
    user_no: i32,
    #[serde(rename = "userName")]
    user_name: String,
    #[serde(rename = "authKey")]
    auth_key: String,
    #[serde(rename = "characterCount")]
    character_count: String,
}

/// 解析角色数量字符串，返回对应最后登录服务器的角色数量
/// 
/// 格式: "lastLoginServer|serverId1,charCount1|serverId2,charCount2|"
fn parse_character_count_for_last_server(credentials: &str) -> u32 {
    if credentials.is_empty() {
        return 0;
    }

    let parts: Vec<&str> = credentials.split('|').collect();
    
    // 获取最后登录的服务器ID
    let last_server_id = parts.first()
        .and_then(|s| s.parse::<u32>().ok())
        .unwrap_or(0);
    
    // 查找对应服务器的角色数量
    for i in 1..parts.len() {
        if !parts[i].is_empty() && parts[i].contains(',') {
            let server_parts: Vec<&str> = parts[i].split(',').collect();
            if server_parts.len() == 2 {
                if let (Ok(server_id), Ok(char_count)) = (
                    server_parts[0].parse::<u32>(),
                    server_parts[1].parse::<u32>()
                ) {
                    if server_id == last_server_id {
                        info!("找到最后登录服务器 {} 的角色数量: {}", server_id, char_count);
                        return char_count;
                    }
                }
            }
        }
    }
    
    info!("未找到服务器 {} 的角色数量，返回0", last_server_id);
    0
}

// 设置用户认证信息的Tauri命令
#[tauri::command]
async fn set_auth_info(params: AuthInfoParams) -> Result<(), String> {
    // 在移动之前克隆用于日志记录的值
    let user_name_for_log = params.user_name.clone();
    let character_count_for_log = params.character_count.clone();
    
    let mut auth_info = GLOBAL_AUTH_INFO.write().unwrap();
    auth_info.user_no = params.user_no;
    auth_info.user_name = params.user_name;
    auth_info.auth_key = params.auth_key;
    // 保持原始的角色数量字符串，不要解析为单一数字
    auth_info.character_count = params.character_count;
    
    // 解析角色数量字符串，获取最后登录服务器的角色数量（仅用于日志记录）
    let parsed_char_count = parse_character_count_for_last_server(&character_count_for_log);
    
    info!("设置认证信息 - 用户: {}, 原始角色数据: {}, 解析后角色数量: {}", 
          user_name_for_log, character_count_for_log, parsed_char_count);

    Ok(())
}

// 设置窗口大小的Tauri命令
#[tauri::command]
async fn set_window_size(window: Window, width: f64, height: f64) -> Result<(), String> {
    
    // 设置窗口大小
    window.set_size(tauri::Size::Physical(tauri::PhysicalSize {
        width: width as u32,
        height: height as u32,
    })).map_err(|e| format!("设置窗口大小失败: {}", e))?;
    
    // 居中窗口
    window.center().map_err(|e| format!("居中窗口失败: {}", e))?;
    Ok(())
}

// 隐藏窗口的Tauri命令
#[tauri::command]
async fn hide_window(window: Window) -> Result<(), String> {
    window.hide().map_err(|e| format!("隐藏窗口失败: {}", e))?;
    Ok(())
}

// 显示窗口的Tauri命令
#[tauri::command]
async fn show_window(window: Window) -> Result<(), String> {
    window.show().map_err(|e| format!("显示窗口失败: {}", e))?;
    // 显示后居中窗口
    window.center().map_err(|e| format!("居中窗口失败: {}", e))?;
    Ok(())
}

// 退出程序的Tauri命令
#[tauri::command]
async fn exit_app(app_handle: tauri::AppHandle) -> Result<(), String> {
    println!("接收到退出程序请求");
    app_handle.exit(0);
    Ok(())
}

// 根据游戏路径获取进程PID
fn get_game_process_pid(game_path: &str) -> Result<u32, String> {
    info!("尝试获取游戏进程PID，路径: {}", game_path);
    
    let mut system = sysinfo::System::new_all();
    system.refresh_all();
    
    let exe_name = std::path::Path::new(game_path)
        .file_name()
        .and_then(|name| name.to_str())
        .unwrap_or("TERA.exe");
        
    info!("查找可执行文件: {}", exe_name);
        
    // 按照可执行文件名称查找
    for (pid, process) in system.processes() {
        if let Some(proc_exe) = process.exe().file_name() {
            if let Some(proc_name) = proc_exe.to_str() {
                if proc_name.eq_ignore_ascii_case(exe_name) {
                    let process_id = pid.as_u32();
                    info!("通过sysinfo找到游戏进程PID: {}", process_id);
                    return Ok(process_id);
                }
            }
        }
    }
    
    info!("通过sysinfo未找到游戏进程");
    Ok(0)
}


// 启动游戏
#[tauri::command]
async fn launch_game(
    app_handle: tauri::AppHandle,
    region: Option<String>
) -> Result<String, String> {
    // 使用静态变量或者全局状态管理启动状态
    static IS_LAUNCHING: std::sync::atomic::AtomicBool = std::sync::atomic::AtomicBool::new(false);

    // 使用原子操作检查和设置启动状态
    if IS_LAUNCHING.compare_exchange(
        false, 
        true, 
        std::sync::atomic::Ordering::SeqCst, 
        std::sync::atomic::Ordering::SeqCst
    ).is_err() {
        return Err("游戏正在启动中".to_string());
    }

    let game_lang = region.unwrap_or_else(|| "en".to_string());

    // 获取认证信息
    let auth_info = GLOBAL_AUTH_INFO.read().unwrap();
    let account_name = auth_info.user_no.to_string();
    let characters_count = auth_info.character_count.clone();
    let ticket = auth_info.auth_key.clone();

    // 获取启动器可执行文件的同级目录下的 Binaries\TERA.exe
    let exe_path = std::env::current_exe()
        .map_err(|e| format!("获取可执行文件路径失败: {}", e))?;
    let exe_dir = exe_path.parent()
        .ok_or("无法获取可执行文件目录")?;
    let game_path = exe_dir.join("Binaries").join("TERA.exe");
    let game_path_str = game_path.to_str()
        .ok_or("无效的游戏路径")?
        .to_string();

    let app_handle_clone = app_handle.clone();

    // 创建停止信号，用于控制端口转发服务
    let port_forward_shutdown = std::sync::Arc::new(std::sync::atomic::AtomicBool::new(false));
    let port_forward_shutdown_clone = port_forward_shutdown.clone();
    
    // 启动端口转发服务
    tokio::spawn(async move {
        if let Err(e) = crate::utils::start_port_forward(
            "127.31.64.183:57801",
            "tera.kvplay.com",
            7801,
            port_forward_shutdown_clone
        ).await {
            error!("端口转发服务启动失败: {}", e);
        }
    });

    // 启动异步检测任务
    let window = app_handle.get_window("main").unwrap();
    let game_path_str_clone = game_path_str.clone();
    tokio::spawn(async move {
        // 等待游戏启动
        let mut attempts = 0;
        let max_attempts = 60; // 最多等待5分钟
        
        while attempts < max_attempts {
            // 重新获取游戏进程PID
            match get_game_process_pid(&game_path_str_clone) {
                Ok(current_pid) if current_pid > 0 => {
                    info!("检测到游戏进程，PID: {}", current_pid);
                    
                    // 开始外挂检测
                    loop {
                        // 首先检查游戏进程是否还存在
                        if !crate::utils::is_process_running(current_pid) {
                            info!("游戏进程 {} 已退出，停止外挂检测", current_pid);
                            // 停止端口转发服务
                            port_forward_shutdown.store(true, std::sync::atomic::Ordering::Relaxed);
                            return;
                        }
                        
                        match crate::utils::detect_game_proxy_hijack_by_pid_with_whitelist(current_pid).await {
                            Ok(true) => {
                                // 检测到代理外挂
                                                                let mut system = sysinfo::System::new_all();
                                system.refresh_process_specifics(sysinfo::Pid::from_u32(current_pid), sysinfo::ProcessRefreshKind::new());
                                if let Some(process) = system.process(sysinfo::Pid::from_u32(current_pid)) {
                                    process.kill();
                                }
                                let _ = window.emit("cheat_detected", "禁止使用一切外挂或辅助程序(包括vpn代理工具)，请关闭后重试!");
                                // 停止端口转发服务
                                port_forward_shutdown.store(true, std::sync::atomic::Ordering::Relaxed);
                                return;
                            }
                            Ok(false) => {}
                            Err(e) => {
                                // 只记录日志，不通知前端
                                error!("外挂检测异常: {}", e);
                                // 停止端口转发服务
                                port_forward_shutdown.store(true, std::sync::atomic::Ordering::Relaxed);
                                return;
                            }
                        }
                        tokio::time::sleep(std::time::Duration::from_secs(5)).await;
                    }
                }
                Ok(_) => {
                    // PID为0，游戏还未启动，继续等待
                    tokio::time::sleep(std::time::Duration::from_secs(5)).await;
                    attempts += 1;
                }
                Err(e) => {
                    error!("获取游戏进程PID失败: {}", e);
                    tokio::time::sleep(std::time::Duration::from_secs(5)).await;
                    attempts += 1;
                }
            }
        }
        
        error!("等待游戏启动超时");
        // 停止端口转发服务
        port_forward_shutdown.store(true, std::sync::atomic::Ordering::Relaxed);
    });

    tokio::task::spawn(async move {
        // 发送游戏状态变更事件
        if let Err(e) = app_handle_clone.emit_all("game_status_changed", true) {
            error!("发送游戏状态变更事件失败: {:?}", e);
        }

        // 隐藏启动器窗口
        if let Some(window) = app_handle_clone.get_window("main") {
            if let Err(e) = window.hide() {
                error!("隐藏启动器窗口失败: {:?}", e);
            } else {
                info!("启动器窗口已隐藏");
                // 发送窗口隐藏事件
                if let Err(e) = app_handle_clone.emit_all("window_hidden", ()) {
                    error!("发送窗口隐藏事件失败: {:?}", e);
                }
            }
        }

        match run_game(
            &account_name,
            &characters_count,
            &ticket,
            &game_lang,
            &game_path_str
        ).await {
            Ok(exit_status) => {
                let result = format!("游戏退出，状态: {:?}", exit_status);
                app_handle_clone.emit_all("game_status", &result).unwrap();
                info!("{}", result);
            }
            Err(e) => {
                let error = format!("启动游戏时出错: {:?}", e);
                app_handle_clone.emit_all("game_status", &error).unwrap();
                error!("{}", error);
            }
        }

        // 游戏结束后显示启动器窗口
        if let Some(window) = app_handle_clone.get_window("main") {
            if let Err(e) = window.show() {
                error!("显示启动器窗口失败: {:?}", e);
            } else {
                info!("启动器窗口已显示");
                // 显示后居中窗口
                if let Err(e) = window.center() {
                    error!("居中启动器窗口失败: {:?}", e);
                }
                // 发送窗口显示事件
                if let Err(e) = app_handle_clone.emit_all("window_shown", ()) {
                    error!("发送窗口显示事件失败: {:?}", e);
                }
            }
        }

        // 发送游戏结束事件
        if let Err(e) = app_handle_clone.emit_all("game_ended", ()) {
            error!("发送游戏结束事件失败: {:?}", e);
        }

        // 重置启动状态
        IS_LAUNCHING.store(false, std::sync::atomic::Ordering::SeqCst);

        reset_global_state();
        info!("游戏启动状态已重置");
    });

    Ok("游戏启动命令已发送".to_string())
}

// 启动DPS插件
#[tauri::command]
async fn launch_dps_plugin() -> Result<String, String> {
    info!("收到DPS插件启动请求");
    crate::utils::launch_dps_plugin()
}

// 注意：check_patch 函数现在由 updater 模块提供

fn main() {
    // 单实例互斥锁，防止多开
    let instance = SingleInstance::new("tera_launcher_single_instance").unwrap();
    if !instance.is_single() {
        return;
    }
    
    // 设置日志记录
    let (tera_logger, mut tera_log_receiver) = teralib::setup_logging();
    log::set_boxed_logger(Box::new(tera_logger)).expect("设置日志记录器失败");
    log::set_max_level(log::LevelFilter::Info);

    // 在Tauri应用启动前检查WebView2运行时
    info!("启动前检查WebView2运行时...");
    
    // 运行WebView2诊断
    webview2_installer::WebView2Installer::diagnose_webview2();
    
    // 检查WebView2是否已安装
    if !webview2_installer::WebView2Installer::is_webview2_installed() {
        error!("WebView2运行时未安装，开始自动安装...");
        
        // 使用阻塞的方式安装WebView2
        let rt = tokio::runtime::Runtime::new().unwrap();
        match rt.block_on(webview2_installer::WebView2Installer::check_and_install_webview2()) {
            Ok(_) => {
                info!("WebView2运行时安装成功");
                // WebView2安装已完成，窗口应已经关闭
                // 等待一下再继续，确保窗口已经完全关闭
                std::thread::sleep(std::time::Duration::from_millis(500));
            }
            Err(e) => {
                error!("WebView2运行时安装失败: {}", e);
                // 先关闭进度窗口，再显示错误对话框
                webview2_installer::close_progress_window();
                webview2_installer::show_error_notification(&format!("WebView2运行时安装失败: {}\n\n请手动安装WebView2运行时后重试。", e));
                return;
            }
        }
    } else {
        // WebView2已安装，直接启动，不显示进度窗口
        if let Some(version) = webview2_installer::WebView2Installer::get_webview2_version() {
            info!("WebView2运行时已安装，版本: {}", version);
        } else {
            info!("WebView2运行时已安装，继续启动应用");
        }
    }

    // 获取游戏状态接收器
    let game_status_receiver = get_game_status_receiver();

    tauri::Builder::default()
        .manage(game_status_receiver)
        .setup(|app| {
            #[cfg(debug_assertions)]
            let window = app.get_window("main").unwrap();
            #[cfg(not(debug_assertions))]
            let _window = app.get_window("main").unwrap();
            
            let app_handle = app.handle();

            #[cfg(debug_assertions)]
            window.open_devtools();

            // 处理日志消息
            tauri::async_runtime::spawn(async move {
                while let Some(log_message) = tera_log_receiver.recv().await {
                    // 输出到控制台
                    println!("{}", log_message);
                    // 发送到前端
                    let _ = app_handle.emit_all("log_message", log_message);
                }
            });

            // println!("Tauri应用初始化完成");
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            set_auth_info,
            set_window_size,
            hide_window,
            show_window,
            launch_game,
            launch_dps_plugin,
            check_patch_command,
            start_patch_process_command,
            get_files_to_update_command,
            check_update_required_command,
            download_all_files_command,
            abort_patch_command,
            check_launcher_update_only,
            check_and_update_launcher,
            get_current_launcher_version,
            get_update_info,
            exit_app,
        ])
        .run(tauri::generate_context!())
        .expect("运行Tauri应用时出错");
}
