[package]
name = "teralaunch"
version = "1.0.1"
description = "KV-Play Exclusive TERA Game Launcher"
authors = ["TNC97"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1", features = [] }

[dependencies]
tauri = { version = "1", features = [ "api-all"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
teralib = { path = "../../teralib" }
once_cell = "1.19"
parking_lot = "0.12.1"
tokio = { version = "1.37.0", features = ["full"] }
tokio-macros = "2.2.0"
log = "0.4.22"
reqwest = { version = "0.12.7", features = ["json", "stream"] }
lazy_static = "1.4.0"
rust-ini = "0.21.0"
blake3 = "1.5.0"
rusqlite = { version = "0.29.0", features = ["bundled"] }
futures-util = "0.3"
indicatif = "0.17.8"
walkdir = "2.5.0"
rayon = "1.10.0"
thiserror = "1.0.63"
env_logger = "0.10.0"
devtools = "0.3.3"
tracing = "0.1"
dotenv = "0.15.0"
self_update = "0.42.0"
semver = "1.0"
tempfile = "3.0"
sysinfo = "0.29"
single-instance = "0.3"
totp-rs = "5.4.0"
data-encoding = "2.4.0"
widestring = "1.0.2"
netstat2 = "0.9.0"
chrono = { version = "0.4", features = ["serde"] }

[target.'cfg(windows)'.dependencies]
windows = { version = "0.52", features = [
    "Win32_Foundation",
    "Win32_UI_Shell",
    "Win32_UI_WindowsAndMessaging",
    "Win32_System_Com",
    "Win32_System_Console",
    "Win32_Graphics_Gdi",
    "Win32_System_LibraryLoader",
] }
webview2-sys = "0.1"

[profile.release]
panic = "abort" # Strip expensive panic clean-up logic
codegen-units = 1 # Compile crates one after another so the compiler can optimize better
lto = true # Enables link to optimizations
opt-level = "s" # Optimize for binary size
strip = true # Remove debug symbols

[features]
# This feature is used for production builds or when a dev server is not specified, DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
custom-menu = []
