﻿# 设置编码为 UTF-8（PowerShell 默认支持）
Write-Output "正在构建 TERA 登陆器发布版本..."

# 切换到子目录
Set-Location -Path ".\teralaunch"

# 设置临时环境变量
$env:TAURI_PRIVATE_KEY = "dW50cnVzdGVkIGNvbW1lbnQ6IHJzaWduIGVuY3J5cHRlZCBzZWNyZXQga2V5ClJXUlRZMEl5dDNsSm1NMFExV28zK2xiMkwyanhLQ2l3amdFeGVFS0RseTZyTzMxcTYwb0FBQkFBQUFBQUFBQUFBQUlBQUFBQWZiTkdQaVNvcHNpSk9kbFFqWGNGbE9FTnFxTm80SHEwN1p6Q0pEY0l5YVJ3VjVtVTVkaVRXc1BwRTZoVSs4KzBJVm0xVmUvTXQrM0ZrY1V6N280NjRtam5JakdIc2hzZkRESiszQVY5Y01WOUxYOEZ6M0pTaERxQXV0bUxCSkMySkF5bjUvaGtuYms9Cg=="
$env:TAURI_KEY_PASSWORD = "2119001"

# 执行构建命令
npm run tauri build

Write-Output "构建完成，发布文件位于：teralaunch\src-tauri\target\release\"
Pause
